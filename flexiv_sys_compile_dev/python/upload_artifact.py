#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import os
import sys
from datetime import datetime, timezone
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), './anyshare_client')))
from anyshare_client import AnyshareClient

REPO_FULL_NAME = os.environ.get("BITBUCKET_REPO_FULL_NAME", "unknown_repo/unknown_name")
# REPO_NAME = REPO_FULL_NAME.split('/')[-1]
REPO_NAME = os.environ.get("BITBUCKET_REPO_SLUG", "unknown_name")
REPO_COMMIT = os.environ.get("BITBUCKET_COMMIT", "unknown_commit")
REPO_SHORT_COMMIT = REPO_COMMIT[:7] if REPO_COMMIT != "unknown_commit" else "no_commit"
REPO_PR_ID = os.environ.get("BITBUCKET_PR_ID", "0")
REPO_BUILD_NUMBER = os.environ.get("BITBUCKET_BUILD_NUMBER", "0")
REPO_ACCESS_TOKEN = os.environ.get("BITBUCKET_REPO_ACCESS_TOKEN", "unknown_token")
UTC_TIMESTAMP = int(datetime.now(timezone.utc).timestamp())

print(f"REPO_FULL_NAME: {REPO_FULL_NAME}")
print(f"REPO_NAME: {REPO_NAME}")
print(f"REPO_COMMIT: {REPO_COMMIT}")
print(f"REPO_SHORT_COMMIT: {REPO_SHORT_COMMIT}")
print(f"REPO_PR_ID: {REPO_PR_ID}")
print(f"REPO_BUILD_NUMBER: {REPO_BUILD_NUMBER}")
print(f"REPO_ACCESS_TOKEN: {REPO_ACCESS_TOKEN}")
print(f"UTC_TIMESTAMP: {UTC_TIMESTAMP}")

def get_artifact_name():
    artifact_filename = f"{REPO_NAME}_{REPO_BUILD_NUMBER}_{REPO_SHORT_COMMIT}_{UTC_TIMESTAMP}.tar.gz"
    return artifact_filename

def upload_artifact_to_bitbucket(artifact_file):
    print("Uploading artifact to Bitbucket...")
    try:
        print("Testing Bitbucket API connectivity...")
        test_response = requests.head("https://api.bitbucket.org", timeout=10)
        test_response.raise_for_status()
        print("Successfully connected to https://api.bitbucket.org")
    except requests.exceptions.RequestException as e:
        print(f"Failed to access https://api.bitbucket.org: {e}")
        print("Please check network connection and try again.")
        return False
    
    if REPO_ACCESS_TOKEN == "unknown_token":
        print("BITBUCKET_REPO_ACCESS_TOKEN is not set")
        return False

    upload_response = requests.post(
        f"https://api.bitbucket.org/2.0/repositories/{REPO_FULL_NAME}/downloads",
        headers={"Authorization": f"Bearer {REPO_ACCESS_TOKEN}"},
        files={"files": (artifact_file, open(artifact_file, "rb"))}
    )
    if upload_response.status_code != 201:
        print(f"Failed to upload artifact to Bitbucket: {upload_response.status_code} {upload_response.text}")
        return False
    print(f"Successfully uploaded artifact to Bitbucket: {upload_response.status_code} {upload_response.text}")
    artifact_file_name = os.path.basename(artifact_file)
    print(f"Download:https://bitbucket.org/{REPO_FULL_NAME}/downloads/{artifact_file_name}")
    return True

def upload_artifact_to_anyshare(artifact_file):
    print("Uploading artifact to AnyShare...")
    return True

def upload_artifact(artifact_file):
    print("Uploading artifact...")

    artifact_filename = get_artifact_name()
    artifact_root_dir = os.path.dirname(artifact_file)
    artifact_file = os.path.basename(artifact_file)
    os.rename(os.path.join(artifact_root_dir, artifact_file), os.path.join(artifact_root_dir, artifact_filename))
    
    bitbucket_upload_artifact_success = upload_artifact_to_bitbucket(os.path.join(artifact_root_dir, artifact_filename))
    if not bitbucket_upload_artifact_success:
        print("Failed to upload artifact to Bitbucket")

    anyshare_upload_artifact_success = upload_artifact_to_anyshare(os.path.join(artifact_root_dir, artifact_filename))
    if not anyshare_upload_artifact_success:
        print("Failed to upload artifact to AnyShare")
    return bitbucket_upload_artifact_success and anyshare_upload_artifact_success

    
if __name__ == "__main__":
    artifact_file = sys.argv[1]
    if not os.path.exists(artifact_file):
        print(f"Artifact file {artifact_file} does not exist")
        sys.exit(1)
    if not os.path.isfile(artifact_file):
        print(f"Artifact file {artifact_file} is not a file")
        sys.exit(1)
    if not artifact_file.endswith(".tar.gz"):
        print(f"Artifact file {artifact_file} is not a tar.gz file")
        sys.exit(1)
    return_code = 0 if upload_artifact(artifact_file) else 1
    sys.exit(return_code)
