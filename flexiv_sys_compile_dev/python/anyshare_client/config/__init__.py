"""
AnyShare Configuration Module

This module provides configuration management for the AnyShare client.
The Config class handles all connection settings, authentication tokens,
and client behavior configuration.

Example:
    Basic configuration:

    >>> from anyshare_client.config import Config
    >>> config = Config()
    >>> config.host = "your-anyshare-host.com"
    >>> config.port = 88
    >>> config.token_id = "your-token-id"
"""

from .config import Config

__all__ = ["Config"]