#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

class Config:
    DEFAULT_HOST = "http://files.flexiv.cloud"
    DEFAULT_PORT = 88
    DEFAULT_BASE_URL = "/api/v1"
    DEFAULT_USERNAME = os.environ.get("AS_USERNAME", "username")
    DEFAULT_PASSWORD = os.environ.get("AS_PASSWORD", "password")
    DEFAULT_TOKEN_ID = os.environ.get("AS_TOKEN_ID", "token_id")
    DEFAULT_USER_ID = os.environ.get("AS_USER_ID", "user_id")
    _CONFIG_DIR = os.path.dirname(os.path.abspath(__file__))
    DEFAULT_ANYSMARE_PUB_KEY_PATH = os.path.join(_CONFIG_DIR, "anyshare_pub.key")

    def __init__(self, 
                host=DEFAULT_HOST, 
                port=DEFAULT_PORT, 
                base_url=DEFAULT_BASE_URL, 
                username=DEFAULT_USERNAME, 
                password=DEFAULT_PASSWORD, 
                anyshare_pub_key_path=DEFAULT_ANYSMARE_PUB_KEY_PATH,
                token_id="", 
                user_id=""):
        self.host = host.strip('/')
        self.port = port
        self.base_url = '/' + base_url.strip('/')
        self.username = username
        self.password = password
        self.anyshare_pub_key_path = anyshare_pub_key_path
        self.token_id = token_id if token_id else self.DEFAULT_TOKEN_ID
        self.user_id = user_id if user_id else self.DEFAULT_USER_ID
        self.token_id_expires = ""

    def get_api_full_base_url(self):
        """Returns the full base URL, e.g., http://files.flexiv.cloud:88/api/v1"""
        return f"{self.host}:{self.port}{self.base_url}".rstrip('/')

if __name__ == "__main__":
    config = Config()
    print(config.get_api_full_base_url())