#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from anyshare_client import Config, <PERSON>th<PERSON><PERSON>, <PERSON>DocApi, Directory<PERSON><PERSON>, <PERSON>lock<PERSON>pi, FileTransferApi
from anyshare_client.api.models import DirCreatemultileveldirReq, DirListReq # Import necessary models
from typing import Literal
import datetime
import traceback

# 导入各个模块的测试函数
from test_anyshare_auth import run_auth_tests
from test_anyshare_entrydoc import run_entrydoc_tests
from test_anyshare_directory import run_directory_tests
from test_anyshare_autolock import run_autolock_tests

def log(level: Literal['INFO', 'ERROR', 'SUCCESS', 'WARNING', 'DEBUG'] = 'INFO',message: str = '') -> None:
    """通用日志函数"""
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    COLORS = {
        'RESET': '\033[0m',
        'RED': '\033[91m',
        'GREEN': '\033[92m',
        'YELLOW': '\033[93m',
        'BLUE': '\033[94m',
        'MAGENTA': '\033[95m',
        'CYAN': '\033[96m',
    }
    color = {
        'INFO': COLORS['BLUE'],
        'ERROR': COLORS['RED'],
        'SUCCESS': COLORS['GREEN'],
        'WARNING': COLORS['YELLOW'],
        'DEBUG': COLORS['MAGENTA']
    }.get(level, COLORS['RESET'])
    print(f"{color}[{timestamp}] [{level}] {message}{COLORS['RESET']}")

if __name__ == "__main__":
    log('INFO', "--- 开始 Anyshare API 测试套件 ---")
    
    # --- 配置初始化 ---
    log('INFO', "1. 初始化配置...")
    config = Config(host="http://files.flexiv.cloud", port=88, base_url="/api/v1")
    # !!! 再次强调: 确保 Token 和 User ID 有效 !!!
    config.token_id = "token_id" 
    config.user_id = "user_id"    
    config.token_id_expires = 3600
    if config.token_id == "YOUR_VALID_TOKEN_ID" or config.user_id == "YOUR_VALID_USER_ID":
        log('WARNING', "使用占位符 Token/User ID。请确保已正确配置或启用了登录。")
        # 如果未配置 Token，可能需要在此处退出或执行登录

    # --- API 客户端初始化 ---
    log('INFO', "2. 初始化 API 客户端...")
    auth_api = AuthApi(config)
    entry_doc_api = EntryDocApi(config)
    directory_api = DirectoryApi(config)
    autolock_api = AutolockApi(config)
    file_transfer_api = FileTransferApi(config)
    
    # --- 运行测试模块 ---
    parent_doc_id_for_dirs = None # This will be "User" personal space docid
    all_tests_passed = True
    
    try:
        # 运行 Auth 测试 (如果需要)
        log('INFO', "\n--- 运行认证 API 测试 --- ")
        auth_passed = run_auth_tests(config, auth_api, log)
        if not auth_passed:
             all_tests_passed = False
             log('ERROR', "Auth API 测试失败，可能影响后续测试。")
             # 根据策略，可以选择是否继续
        
        # 运行 EntryDoc 测试并获取父目录 ID ("User" personal space docid)
        log('INFO', "\n--- 运行 EntryDoc API 测试 --- ")
        parent_doc_id_for_dirs = run_entrydoc_tests(config, entry_doc_api, log)
        if parent_doc_id_for_dirs is None:
            log('ERROR', "EntryDoc API 测试未能成功获取用于目录测试的父目录ID (预期为 'User' 的空间ID)。")
            all_tests_passed = False 
        else:
            log('SUCCESS', f"EntryDoc 测试成功获取父目录 ID ('User' 的空间ID): {parent_doc_id_for_dirs}")

        # 运行 Directory 测试 (如果获取到父目录ID)
        if parent_doc_id_for_dirs:
            log('INFO', "\n--- 运行 Directory API 测试 --- ")
            dir_passed = run_directory_tests(config, directory_api, parent_doc_id_for_dirs, log)
            if not dir_passed:
                all_tests_passed = False
                log('ERROR', "Directory API 测试报告失败。")
        else:
            log('WARNING', "跳过 Directory API 测试，因为未能从 EntryDoc 测试中获取有效的父目录ID。")

        # --- 设置 Autolock 测试所需的文件和目录 ---
        target_autolock_dir_name = "autolock_dir_test"
        target_autolock_file_name = "autolock_file_test.txt"
        actual_dir_docid_for_autolock = None
        actual_file_docid_for_autolock = None

        if parent_doc_id_for_dirs:
            log('INFO', f"为 Autolock 测试准备目录 '{target_autolock_dir_name}' 于 '{parent_doc_id_for_dirs}' 下...")
            try:
                dir_create_req = DirCreatemultileveldirReq(docid=parent_doc_id_for_dirs, path=target_autolock_dir_name)
                dir_create_res = directory_api.create_multilevel_directory(dir_create_req)
                if dir_create_res and dir_create_res.docid:
                    actual_dir_docid_for_autolock = dir_create_res.docid
                    log('SUCCESS', f"  目录 '{target_autolock_dir_name}' 创建/获取成功: ID {actual_dir_docid_for_autolock}")
                    
                    # 尝试查找文件
                    log('INFO', f"  在目录 '{actual_dir_docid_for_autolock}' 中查找文件 '{target_autolock_file_name}'...")
                    list_req = DirListReq(docid=actual_dir_docid_for_autolock, attr=False) # attr=True might be useful
                    list_res = directory_api.list_directory_contents(list_req)
                    if list_res and list_res.files:
                        found_file = None
                        for f_item in list_res.files:
                            if f_item.name == target_autolock_file_name:
                                found_file = f_item
                                break
                        if found_file and found_file.docid:
                            actual_file_docid_for_autolock = found_file.docid
                            log('SUCCESS', f"  文件 '{target_autolock_file_name}' 找到: ID {actual_file_docid_for_autolock}")
                        else:
                            log('WARNING', f"  文件 '{target_autolock_file_name}' 未在目录 '{target_autolock_dir_name}' (ID: {actual_dir_docid_for_autolock}) 中找到。")
                            log('WARNING', f"  请确保文件 '{target_autolock_file_name}' 已存在于该位置以进行完整的文件锁定测试。")
                    else:
                        log('WARNING', f"  无法列出目录 '{target_autolock_dir_name}' (ID: {actual_dir_docid_for_autolock}) 的内容，或目录为空。")
                else:
                    log('ERROR', f"  创建/获取目录 '{target_autolock_dir_name}' 失败。响应: {dir_create_res}")
                    all_tests_passed = False
            except Exception as e_setup:
                log('ERROR', f"  设置 Autolock 测试目录/文件时出错: {e_setup}")
                all_tests_passed = False
        else:
            log('WARNING', "跳过 Autolock 测试的文件/目录设置，因为 'User' 的空间ID 未获取到。")
            
        # 运行 Autolock 测试
        log('INFO', "\n--- 运行 Autolock API 测试 ---")
        # Pass the dynamically obtained docids, or None if not found/created
        autolock_passed = run_autolock_tests(config, autolock_api, 
                                             actual_file_docid_for_autolock,
                                             actual_dir_docid_for_autolock,
                                             log)
        if not autolock_passed:
            all_tests_passed = False
            # Log message is already inside run_autolock_tests or handled by its return value
        # else:
            # log('SUCCESS', "Autolock API 测试通过或按预期跳过部分内容。") # More nuanced logging can be done

    except Exception as e:
        log('ERROR', f"---!!! 主测试运行器发生未处理的严重错误: {e} !!!---")
        log('ERROR', traceback.format_exc())
        all_tests_passed = False

    # --- 测试总结 --- 
    log('INFO', "\n--- 测试套件执行完毕 ---")
    if all_tests_passed:
        log('SUCCESS', "所有测试模块均报告成功或已按预期处理。")
    else:
        log('ERROR', "部分或全部测试模块报告失败或设置步骤失败。请检查上面的日志获取详细信息。")

    # --- 可选的最终登出 --- 
    # if config.token_id and config.token_id != "YOUR_VALID_TOKEN_ID": 
    #     log('INFO', "\n执行最终登出...")
    #     logout_res = auth_api.logout()
    #     log('SUCCESS' if logout_res else 'ERROR', f"最终登出结果: {logout_res}")

