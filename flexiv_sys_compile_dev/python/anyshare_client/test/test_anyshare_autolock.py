#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from anyshare_client import <PERSON>lockApi, Config
from anyshare_client.api.models import (
    AutolockLockReq,
    AutolockTrylockReq, 
    AutolockRefreshReq,
    AutolockUnlockReq,
    AutolockGetlockinfoReq,
    AutolockGetdirlockinfoReq,
    AutolockGetlockedfileinfosReq,
    AutolockTrylockRes,
    AutolockRefreshRes,
    AutolockGetlockinfoRes,
    AutolockGetdirlockinfoRes,
    AutolockGetlockedfileinfosRes,
    Error
)
import time
# dataclasses is no longer needed for these models as they are Pydantic based
import traceback
from typing import Callable

def run_autolock_tests(config: Config, autolock_api: AutolockApi, 
                         test_file_docid: str, test_dir_docid: str, log: Callable):
    log('INFO', f"--- 开始文件锁 API 测试 (操作文件 ID: {test_file_docid}, 操作目录 ID: {test_dir_docid}) ---")
    test_succeeded_overall = True

    if not test_file_docid:
        log('ERROR', "未提供测试文件 docid，部分文件锁测试将跳过。")
        #return False # Or decide how to handle this, maybe skip some tests
    if not test_dir_docid:
        log('ERROR', "未提供测试目录 docid，获取目录锁信息测试将跳过。")

    try:
        # 0. 获取当前用户已锁定的文件（测试前状态）
        log('INFO', "0. 获取当前用户所有文件锁信息 (测试前):")
        locked_files_req = AutolockGetlockedfileinfosReq(limit=10) # Define req object
        locked_files_response = autolock_api.get_locked_file_infos(locked_files_req)

        if isinstance(locked_files_response, AutolockGetlockedfileinfosRes):
            log('SUCCESS', f"  获取成功: 共 {len(locked_files_response.docinfos)} 个锁定信息")
            for item in locked_files_response.docinfos:
                log('DEBUG', f"    - {item.path} (ID: {item.docid}) by {item.lockername} ({item.lockerid})")
        elif isinstance(locked_files_response, Error):
            log('WARNING', f"  获取当前用户锁定文件信息时出错: {locked_files_response.errmsg}")
        elif isinstance(locked_files_response, str):
            log('WARNING', f"  获取当前用户锁定文件信息时返回字符串错误: {locked_files_response}")
        else:
            log('WARNING', f"  获取当前用户锁定文件信息失败或无结果。响应类型: {type(locked_files_response)}, 内容: {locked_files_response}")
            # This is not a test failure itself, just informational for now

        if test_file_docid:
            # 1. 尝试锁定文件 (正常情况)
            log('INFO', f"1. 尝试锁定文件 '{test_file_docid}':")
            try_lock_req = AutolockTrylockReq(docid=test_file_docid)
            try_lock_response = autolock_api.try_lock_file(try_lock_req)
            if isinstance(try_lock_response, AutolockTrylockRes):
                if try_lock_response.issucceed:
                    log('SUCCESS', f"  文件 '{test_file_docid}' 尝试锁定成功。")
                else:
                    log('WARNING', f"  文件 '{test_file_docid}' 尝试锁定失败，可能已被锁定: User: {try_lock_response.lockername} ({try_lock_response.lockerid})")
            elif isinstance(try_lock_response, Error):
                log('ERROR', f"  文件 '{test_file_docid}' 尝试锁定出错: {try_lock_response.errmsg}")
                test_succeeded_overall = False
            elif isinstance(try_lock_response, str):
                log('ERROR', f"  文件 '{test_file_docid}' 尝试锁定返回字符串错误: {try_lock_response}")
                test_succeeded_overall = False
            else:
                log('ERROR', f"  文件 '{test_file_docid}' 尝试锁定失败。未知响应: {try_lock_response}")
                test_succeeded_overall = False

            # 2. 获取文件锁信息 (应该已锁定或按 try_lock_response 的结果)
            log('INFO', f"2. 获取文件 '{test_file_docid}' 的锁定信息:")
            get_lock_info_req = AutolockGetlockinfoReq(docid=test_file_docid)
            get_lock_info_response = autolock_api.get_lock_info(get_lock_info_req)
            if isinstance(get_lock_info_response, AutolockGetlockinfoRes):
                log('SUCCESS', f"  获取锁定信息成功: islocked={get_lock_info_response.islocked}")
                if get_lock_info_response.islocked:
                    log('DEBUG', f"    Locker: {get_lock_info_response.lockername} ({get_lock_info_response.lockerid})")
            elif isinstance(get_lock_info_response, Error):
                log('ERROR', f"  获取文件 '{test_file_docid}' 锁定信息出错: {get_lock_info_response.errmsg}")
                test_succeeded_overall = False
            elif isinstance(get_lock_info_response, str):
                log('ERROR', f"  获取文件 '{test_file_docid}' 锁定信息返回字符串错误: {get_lock_info_response}")
                test_succeeded_overall = False
            else:
                log('ERROR', f"  获取文件 '{test_file_docid}' 锁定信息失败。未知响应: {get_lock_info_response}")
                test_succeeded_overall = False

            # 3. 刷新文件锁 (假设文件已被当前用户锁定)
            log('INFO', f"3. 刷新文件 '{test_file_docid}' 的文件锁:")
            refresh_req = AutolockRefreshReq(lockinfos=[test_file_docid])
            refresh_response = autolock_api.refresh_file_lock(refresh_req)
            if isinstance(refresh_response, AutolockRefreshRes):
                if refresh_response.lockinfos:
                    refreshed_info = next((info for info in refresh_response.lockinfos if info.docid == test_file_docid), None)
                    if refreshed_info and refreshed_info.state == 0:
                        log('SUCCESS', f"  文件 '{test_file_docid}' 刷新锁成功。")
                    elif refreshed_info:
                        log('ERROR', f"  文件 '{test_file_docid}' 刷新锁失败: state={refreshed_info.state}, msg='{refreshed_info.errmsg}'")
                        test_succeeded_overall = False
                    else:
                        log('ERROR', f"  刷新锁响应中未找到文件 '{test_file_docid}' 的信息。响应: {refresh_response}")
                        test_succeeded_overall = False
                else:
                    log('ERROR', f"  刷新锁响应中 lockinfos 为空。响应: {refresh_response}")
                    test_succeeded_overall = False
            elif isinstance(refresh_response, Error):
                log('ERROR', f"  刷新文件锁出错: {refresh_response.errmsg}")
                test_succeeded_overall = False
            elif isinstance(refresh_response, str):
                log('ERROR', f"  刷新文件锁返回字符串错误: {refresh_response}")
                test_succeeded_overall = False
            else:
                log('ERROR', f"  刷新文件锁失败或响应格式不正确。未知响应: {refresh_response}")
                test_succeeded_overall = False

            # 4. 强制锁定文件
            log('INFO', f"4a. (准备) 先解锁文件 '{test_file_docid}' 以测试强制锁定:")
            unlock_req_prep = AutolockUnlockReq(docid=test_file_docid)
            unlock_response_prep = autolock_api.unlock_file(unlock_req_prep)
            if unlock_response_prep is True:
                log('INFO', "    解锁 (准备) 成功")
            elif isinstance(unlock_response_prep, Error):
                 log('WARNING', f"    解锁 (准备) 时出错: {unlock_response_prep.errmsg} - 可能文件未被当前用户锁定或已解锁")
            elif isinstance(unlock_response_prep, str):
                 log('WARNING', f"    解锁 (准备) 时返回字符串: {unlock_response_prep} - 可能文件未被当前用户锁定或已解锁")
            elif unlock_response_prep is False: # Explicitly False might mean an issue if True is expected for success no-body
                 log('WARNING', "    解锁 (准备) 返回 False - 可能文件未被当前用户锁定或已解锁")
            else: # None could be an issue if True is expected
                 log('WARNING', f"    解锁 (准备) 返回意外结果: {unlock_response_prep} - 可能文件未被当前用户锁定或已解锁")

            log('INFO', f"4b. 强制锁定文件 '{test_file_docid}':")
            force_lock_req = AutolockLockReq(docid=test_file_docid, force=True, expiretime=-1)
            force_lock_response = autolock_api.lock_file(force_lock_req)
            if force_lock_response is True:
                log('SUCCESS', f"  文件 '{test_file_docid}' 强制锁定成功。")
                verify_lock_info_resp = autolock_api.get_lock_info(AutolockGetlockinfoReq(docid=test_file_docid))
                if isinstance(verify_lock_info_resp, AutolockGetlockinfoRes) and verify_lock_info_resp.islocked:
                    log('DEBUG', f"    验证成功: 文件已锁定 by {verify_lock_info_resp.lockername}")
                elif isinstance(verify_lock_info_resp, AutolockGetlockinfoRes):
                    log('ERROR', f"    验证失败: 强制锁定后，文件状态查询为未锁定。islocked={verify_lock_info_resp.islocked}")
                    test_succeeded_overall = False
                else:
                    log('ERROR', f"    验证强制锁定失败: 获取状态信息时出错或类型不匹配。 {verify_lock_info_resp}")
                    test_succeeded_overall = False
            elif isinstance(force_lock_response, Error):
                log('ERROR', f"  文件 '{test_file_docid}' 强制锁定出错: {force_lock_response.errmsg}")
                test_succeeded_overall = False
            elif isinstance(force_lock_response, str):
                log('ERROR', f"  文件 '{test_file_docid}' 强制锁定返回字符串错误: {force_lock_response}")
                test_succeeded_overall = False
            else:
                log('ERROR', f"  文件 '{test_file_docid}' 强制锁定失败。未知响应: {force_lock_response}")
                test_succeeded_overall = False
            
            # 5. 解锁文件
            log('INFO', f"5. 解锁文件 '{test_file_docid}':")
            unlock_req = AutolockUnlockReq(docid=test_file_docid)
            unlock_response = autolock_api.unlock_file(unlock_req)
            if unlock_response is True:
                log('SUCCESS', f"  文件 '{test_file_docid}' 解锁成功。")
                verify_unlock_info_resp = autolock_api.get_lock_info(AutolockGetlockinfoReq(docid=test_file_docid))
                if isinstance(verify_unlock_info_resp, AutolockGetlockinfoRes) and not verify_unlock_info_resp.islocked:
                    log('DEBUG', f"    验证成功: 文件已解锁。")
                elif isinstance(verify_unlock_info_resp, AutolockGetlockinfoRes):
                    log('ERROR', f"    验证失败: 解锁后，文件状态查询仍为锁定。Locker: {verify_unlock_info_resp.lockername}")
                    test_succeeded_overall = False
                else:
                    log('ERROR', f"    验证解锁失败: 获取状态信息时出错或类型不匹配。 {verify_unlock_info_resp}")
                    test_succeeded_overall = False
            elif isinstance(unlock_response, Error):
                log('ERROR', f"  文件 '{test_file_docid}' 解锁出错: {unlock_response.errmsg}")
                test_succeeded_overall = False
            elif isinstance(unlock_response, str):
                log('ERROR', f"  文件 '{test_file_docid}' 解锁返回字符串错误: {unlock_response}")
                test_succeeded_overall = False
            else:
                log('ERROR', f"  文件 '{test_file_docid}' 解锁失败。未知响应: {unlock_response}")
                test_succeeded_overall = False
        else:
            log('WARNING', "跳过基于特定文件的锁定/解锁测试，因为 test_file_docid 未提供。")

        # 6. 获取文件夹锁信息
        if test_dir_docid:
            log('INFO', f"6. 获取文件夹 '{test_dir_docid}' 的锁定信息:")
            dir_lock_info_req = AutolockGetdirlockinfoReq(docid=test_dir_docid)
            dir_lock_info_response = autolock_api.get_dir_lock_info(dir_lock_info_req)
            if isinstance(dir_lock_info_response, AutolockGetdirlockinfoRes):
                log('SUCCESS', f"  获取文件夹锁信息成功: islocked={dir_lock_info_response.islocked}")
                log('INFO', f"    (此文件夹内 {'有' if dir_lock_info_response.islocked else '没有'} 文件被锁定)")
            elif isinstance(dir_lock_info_response, Error):
                log('ERROR', f"  获取文件夹 '{test_dir_docid}' 锁定信息出错: {dir_lock_info_response.errmsg}")
                test_succeeded_overall = False
            elif isinstance(dir_lock_info_response, str):
                log('ERROR', f"  获取文件夹 '{test_dir_docid}' 锁定信息返回字符串错误: {dir_lock_info_response}")
                test_succeeded_overall = False
            else:
                log('ERROR', f"  获取文件夹 '{test_dir_docid}' 锁定信息失败。未知响应: {dir_lock_info_response}")
                test_succeeded_overall = False
        else:
            log('WARNING', "跳过获取文件夹锁信息测试，因为 test_dir_docid 未提供。")

        # 7. 获取当前用户锁信息 (分页测试)
        log('INFO', "7. 获取当前用户锁信息 (分页测试, limit=1, start=0):")
        paginated_lock_req = AutolockGetlockedfileinfosReq(start=0, limit=1)
        paginated_lock_response = autolock_api.get_locked_file_infos(paginated_lock_req)
        if isinstance(paginated_lock_response, AutolockGetlockedfileinfosRes):
            log('SUCCESS', f"  获取分页锁定信息成功: 返回 {len(paginated_lock_response.docinfos)} 条记录 (期望最多1条)")
            if len(paginated_lock_response.docinfos) > 1:
                log('ERROR', f"    分页限制错误，期望最多1条，实际返回 {len(paginated_lock_response.docinfos)}")
                test_succeeded_overall = False
            for item in paginated_lock_response.docinfos:
                log('DEBUG', f"    - {item.path} (ID: {item.docid}) by {item.lockername}")
        elif isinstance(paginated_lock_response, Error):
            log('WARNING', f"  获取分页锁定信息时出错: {paginated_lock_response.errmsg}")
            # Not necessarily a failure if user has no locks, but API call should provide a clear error or empty list
        elif isinstance(paginated_lock_response, str):
            log('WARNING', f"  获取分页锁定信息时返回字符串错误: {paginated_lock_response}")
        else:
            log('WARNING', f"  获取分页锁定信息失败或无结果。未知响应: {paginated_lock_response}")
            # Not necessarily a failure if user has no locks, but API call should succeed

    except Exception as e:
        log('ERROR', f"---!!! 文件锁 API 测试中发生严重错误: {e} !!!---")
        log('ERROR', traceback.format_exc())
        test_succeeded_overall = False
    
    finally:
        log('INFO', "--- 清理阶段 (文件锁测试) ---")
        if test_file_docid:
            # 尝试确保测试文件最终是解锁状态
            log('INFO', f"  尝试最终解锁文件 '{test_file_docid}' 以清理测试状态...")
            try:
                current_lock_status_resp = autolock_api.get_lock_info(AutolockGetlockinfoReq(docid=test_file_docid))
                if isinstance(current_lock_status_resp, AutolockGetlockinfoRes) and current_lock_status_resp.islocked:
                    log('INFO', f"    文件 '{test_file_docid}' 当前被 {current_lock_status_resp.lockername} 锁定。尝试解锁...")
                    unlock_final_req = AutolockUnlockReq(docid=test_file_docid)
                    unlock_final_response = autolock_api.unlock_file(unlock_final_req)
                    if unlock_final_response is True:
                        log('INFO', f"    最终解锁文件 '{test_file_docid}' 成功")
                    elif isinstance(unlock_final_response, Error):
                        log('WARNING', f"    最终解锁文件 '{test_file_docid}' 时出错: {unlock_final_response.errmsg}")
                    elif isinstance(unlock_final_response, str):
                        log('WARNING', f"    最终解锁文件 '{test_file_docid}' 时返回字符串错误: {unlock_final_response}")
                    else:
                        log('WARNING', f"    最终解锁文件 '{test_file_docid}' 失败或无需解锁。响应: {unlock_final_response}")
                elif isinstance(current_lock_status_resp, AutolockGetlockinfoRes):
                    log('INFO', f"    文件 '{test_file_docid}' 在清理时未被锁定。")
                elif isinstance(current_lock_status_resp, Error):
                    log('WARNING', f"    清理时获取文件 '{test_file_docid}' 锁状态出错: {current_lock_status_resp.errmsg}")
                else:
                    log('INFO', f"    文件 '{test_file_docid}' 在清理时无法获取状态或状态未知: {current_lock_status_resp}")
            except Exception as e_cleanup:
                log('WARNING', f"    清理文件锁时发生错误: {e_cleanup}")
        else:
            log('INFO', " 无特定文件ID提供，跳过特定文件解锁清理。")

        log('INFO', f"--- 文件锁 API 测试结束 (总体结果: {'成功' if test_succeeded_overall else '部分失败或完全失败'}) ---")
        return test_succeeded_overall

# 如果此文件被直接运行，可以添加一个简单的本地测试逻辑
if __name__ == '__main__':
    import datetime
    import os

    def local_log(level: str = 'INFO', message: str = '') -> None:
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        print(f"[{timestamp}] [{level}] {message}") 
    
    local_log('INFO', "本地运行 test_anyshare_autolock.py 进行测试...")
    
    # !!重要!!: 运行此独立测试前，请设置以下环境变量或直接修改这些值
    # 为确保测试的有效性，test_file_docid 应该是一个用户有权限操作的真实文件ID
    # test_dir_docid 应该是一个用户有权限操作的真实目录ID
    test_host = os.environ.get("ANYSHARE_HOST", "http://your-anyshare-host.com") # 例如: "http://files.flexiv.cloud"
    test_port = int(os.environ.get("ANYSHARE_PORT", "80"))                  # 例如: 88
    test_base_url = os.environ.get("ANYSHARE_BASEURL", "/api/v1")
    test_token_id = os.environ.get("ANYSHARE_TOKEN_ID", "YOUR_VALID_TOKEN_ID")
    test_user_id = os.environ.get("ANYSHARE_USER_ID", "YOUR_VALID_USER_ID")
    
    # === 需要用户提供这两个ID进行测试 ===
    # 您可以先通过其他方式（如UI操作后查看ID，或使用其他API脚本创建/获取）获得这些ID
    example_file_docid_for_testing = os.environ.get("ANYSHARE_TEST_FILE_DOCID", "") 
    example_dir_docid_for_testing = os.environ.get("ANYSHARE_TEST_DIR_DOCID", "")
    # =====================================

    if test_token_id == "YOUR_VALID_TOKEN_ID" or not test_user_id or \
       test_host == "http://your-anyshare-host.com":
        local_log('ERROR', "请为此本地测试配置 ANYSHARE_HOST, ANYSHARE_PORT, ANYSHARE_TOKEN_ID, ANYSHARE_USER_ID 环境变量。")
        exit(1)
    
    if not example_file_docid_for_testing:
        local_log('WARNING', "环境变量 ANYSHARE_TEST_FILE_DOCID 未设置，部分文件锁测试将无法准确执行。")
    if not example_dir_docid_for_testing:
        local_log('WARNING', "环境变量 ANYSHARE_TEST_DIR_DOCID 未设置，获取目录锁信息测试将无法准确执行。")

    test_config = Config(host=test_host, port=test_port, base_url=test_base_url, 
                         token_id=test_token_id, user_id=test_user_id)
    
    autolock_api_local = AutolockApi(test_config)
    
    run_autolock_tests(test_config, autolock_api_local, 
                       example_file_docid_for_testing, 
                       example_dir_docid_for_testing, 
                       local_log) 