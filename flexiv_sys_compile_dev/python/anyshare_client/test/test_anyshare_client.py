#!/usr/bin/env python3

import sys
import os
import time
import shutil
import hashlib
import binascii
import random
import string
import zipfile
from urllib.parse import urlparse

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from anyshare_client import AnyshareClient
from typing import Literal, Callable, Optional, List, Dict, Any, Tuple
import datetime
import traceback

import logging
logging.basicConfig(level=logging.INFO)


TOKEN_ID = "95c00e8a-97fe-4c09-b234-26a92b88cfe7"
USER_ID = "46288cc8-5ece-11ef-8c9f-dcf401e6cda4"

all_tests_passed_global = True

def log(level: Literal['INFO', 'ERROR', 'SUCCESS', 'WARNING', 'DEBUG'] = 'INFO',message: str = '') -> None:
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    COLORS = {
        'RESET': '\033[0m',
        'RED': '\033[91m',
        'GREEN': '\033[92m',
        'YELLOW': '\033[93m',
        'BLUE': '\033[94m',
        'MAGENTA': '\033[95m',
        'CYAN': '\033[96m',
    }
    color = {
        'INFO': COLORS['BLUE'],
        'ERROR': COLORS['RED'],
        'SUCCESS': COLORS['GREEN'],
        'WARNING': COLORS['YELLOW'],
        'DEBUG': COLORS['MAGENTA']
    }.get(level, COLORS['RESET'])
    print(f"{color}[{timestamp}] [{level}] {message}{COLORS['RESET']}")

def assert_test_result(condition: bool, success_message: str, failure_message: str, test_name: str = ""):
    global all_tests_passed_global
    prefix = f"[{test_name}] " if test_name else ""
    if condition:
        log('SUCCESS', f"{prefix}{success_message}")
    else:
        log('ERROR', f"{prefix}{failure_message}")
        all_tests_passed_global = False

def create_dummy_file(filepath: str, size_bytes: int, log_func: Optional[Callable] = log) -> bool:
    try:
        with open(filepath, 'wb') as f:
            chunk_size = 1024 * 1024
            bytes_written = 0
            while bytes_written < size_bytes:
                bytes_to_write = min(chunk_size, size_bytes - bytes_written)
                if size_bytes > 1 * 1024 * 1024:
                    chunk_data = os.urandom(bytes_to_write)
                else:
                    base_pattern = "AnyShare Client Test Content. ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789. "
                    pattern_bytes = base_pattern.encode('utf-8')
                    if not pattern_bytes: pattern_bytes = b"default"
                    full_pattern_data = (pattern_bytes * (bytes_to_write // len(pattern_bytes) + 1))
                    chunk_data = full_pattern_data[:bytes_to_write]
                f.write(chunk_data)
                bytes_written += len(chunk_data)
        actual_size = os.path.getsize(filepath)
        if actual_size == size_bytes:
            if log_func: log_func('DEBUG', f"    Dummy file '{filepath}' of size {actual_size} bytes created.")
            return True
        else:
            if log_func: log_func('ERROR', f"    Dummy file '{filepath}' size mismatch. Expected {size_bytes}, Got {actual_size}")
            return False
    except IOError as e:
        if log_func: log_func('ERROR', f"    Error creating dummy file {filepath}: {e}")
        return False

def calculate_md5(filepath: str) -> Optional[str]:
    try:
        hash_md5 = hashlib.md5()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except FileNotFoundError:
        log('ERROR', f"    MD5 calculation: File not found at '{filepath}'")
        return None
    except IOError as e:
        log('ERROR', f"    MD5 calculation IOError for '{filepath}': {e}")
        return None

if __name__ == "__main__":
    log('INFO', "--- 开始 AnyshareClient 文件传输功能测试 --- ")
    client = AnyshareClient()

    USERNAME = os.environ.get("ANYSHARE_USERNAME", "username")
    PASSWORD = os.environ.get("ANYSHARE_PASSWORD", "password")
    ANYSHARE_HOST = "http://files.flexiv.cloud"
    ANYSHARE_PORT = 88

    timestamp_str = str(int(time.time()))
    local_base_dir = os.path.abspath(f"./anyshare_client_test_temp_{timestamp_str}")
    local_upload_source_dir = os.path.join(local_base_dir, "uploads")
    local_download_target_dir = os.path.join(local_base_dir, "downloads")

    remote_base_test_dir_name = f"User/anyshare_client_tests_{timestamp_str}"
    remote_base_test_dir_path: Optional[str] = None

    small_file_name = "small_test_file.txt"
    small_file_size_bytes = 1 * 1024 * 1024
    large_file_name = "large_test_file.bin"
    large_file_size_bytes = 6 * 1024 * 1024

    batch_file1_name = "b_file1.txt"
    batch_file2_name = "b_file2.log"
    batch_subdir_name = "b_subdir"
    batch_file3_name = "b_file3_in_subdir.dat"
    batch_file_size = 10 * 1024

    uploaded_files_remote_paths: Dict[str, str] = {}

    try:
        log('INFO', "1. 初始化和配置...")
        client.config_client(host=ANYSHARE_HOST, port=ANYSHARE_PORT)
        client._config.token_id = TOKEN_ID
        client._config.user_id = USER_ID
        client._config.token_id_expires = "3600"
        log('INFO', f"使用预设 Token ID: {TOKEN_ID} (注意: 跳过实际登录)")

        log('INFO', "2. 准备本地和远程测试环境...")
        os.makedirs(local_upload_source_dir, exist_ok=True)
        os.makedirs(local_download_target_dir, exist_ok=True)
        log('DEBUG', f"    本地目录 '{local_upload_source_dir}' 和 '{local_download_target_dir}' 已创建/确认。")

        remote_base_test_dir_path = f"AnyShare://{remote_base_test_dir_name}"
        log('INFO', f"    尝试创建远程主测试目录: {remote_base_test_dir_path}")

        dir_created = client.create_directory(remote_base_test_dir_path)
        assert_test_result(
            dir_created,
            f"远程主测试目录 '{remote_base_test_dir_path}' 创建成功。",
            f"创建远程主测试目录 '{remote_base_test_dir_path}' 失败。",
            "Initial Remote Dir Creation"
        )
        if not dir_created:
            log('ERROR', "远程主测试目录创建失败，后续许多测试可能无法正常进行或会失败。")

        log('INFO', "\n--- 测试 create_directory 更多场景 ---")
        if dir_created:
            already_exists_path = remote_base_test_dir_path
            log('INFO', f"    测试创建已存在的目录: {already_exists_path}")
            res_create_existing = client.create_directory(already_exists_path)
            assert_test_result(
                res_create_existing,
                f"再次创建目录 '{already_exists_path}' 成功 (预期行为: 幂等)。",
                f"再次创建目录 '{already_exists_path}' 失败。",
                "Create Existing Directory"
            )

            nested_dir_name = "nested_dir_for_test"
            remote_nested_dir_path = f"{remote_base_test_dir_path}/{nested_dir_name}"
            log('INFO', f"    测试创建嵌套目录: {remote_nested_dir_path}")
            res_create_nested = client.create_directory(remote_nested_dir_path)
            assert_test_result(
                res_create_nested,
                f"嵌套目录 '{remote_nested_dir_path}' 创建成功。",
                f"嵌套目录 '{remote_nested_dir_path}' 创建失败。",
                "Create Nested Directory"
            )
        else:
            log('WARNING', "跳过部分 create_directory 详细测试，因为远程主测试目录创建失败。")

        invalid_path_format = "InvalidFormatDir/test"
        log('INFO', f"    测试使用无效路径格式创建目录: {invalid_path_format}")
        res_invalid_path = client.create_directory(invalid_path_format)
        assert_test_result(
            not res_invalid_path,
            f"使用无效路径 '{invalid_path_format}' 创建目录按预期失败 (返回 False)。",
            f"使用无效路径 '{invalid_path_format}' 创建目录意外成功。",
            "Create Directory Invalid Path Format"
        )

        log('INFO', "    测试创建根目录 'AnyShare://' (应失败)")
        res_create_root = client.create_directory("AnyShare://")
        assert_test_result(
            not res_create_root,
            "尝试创建根目录 'AnyShare://' 按预期失败。",
            "尝试创建根目录 'AnyShare://' 意外成功。",
            "Create Directory Root"
        )

        log('INFO', "\n--- 测试 path_to_gns ---")
        gns_valid = None
        if dir_created:
            log('INFO', f"    测试 path_to_gns 有效路径: {remote_base_test_dir_path}")
            gns_valid = client.path_to_gns(remote_base_test_dir_path)
            assert_test_result(
                bool(gns_valid),
                f"路径 '{remote_base_test_dir_path}' 成功解析到 GNS: {gns_valid}",
                f"路径 '{remote_base_test_dir_path}' 解析 GNS 失败。",
                "Path to GNS (Valid Existing Dir)"
            )
        else:
            log('WARNING', "跳过 path_to_gns 有效路径测试，因为远程主目录未成功创建。")

        invalid_path_for_gns = "NotAnySharePath/somefile.txt"
        log('INFO', f"    测试 path_to_gns 无效路径格式: {invalid_path_for_gns}")
        gns_invalid_format = client.path_to_gns(invalid_path_for_gns)
        assert_test_result(
            not gns_invalid_format,
            f"无效路径格式 '{invalid_path_for_gns}' 解析 GNS 按预期返回空字符串。",
            f"无效路径格式 '{invalid_path_for_gns}' 解析 GNS 意外返回: {gns_invalid_format}",
            "Path to GNS (Invalid Format)"
        )

        non_existent_path_anyshare_format = f"AnyShare://{remote_base_test_dir_name}/non_existent_file_or_dir_12345.xyz"
        if not dir_created:
            non_existent_path_anyshare_format = f"AnyShare://completely_random_non_existent_path_{timestamp_str}/some_file.txt"

        log('INFO', f"    测试 path_to_gns 不存在的路径 (有效格式): {non_existent_path_anyshare_format}")
        gns_non_existent = client.path_to_gns(non_existent_path_anyshare_format)
        assert_test_result(
            not gns_non_existent,
            f"不存在的路径 '{non_existent_path_anyshare_format}' 解析 GNS 按预期返回空字符串。",
            f"不存在的路径 '{non_existent_path_anyshare_format}' 解析 GNS 意外返回: {gns_non_existent}",
            "Path to GNS (Non-Existent, Valid Format)"
        )

        log('INFO', "\n--- 测试 gns_to_path ---")
        if gns_valid:
            log('INFO', f"    测试 gns_to_path 有效 GNS: {gns_valid} (来自: {remote_base_test_dir_path})")
            path_from_gns = client.gns_to_path(gns_valid)
            normalized_original_path = remote_base_test_dir_path.rstrip('/')
            normalized_converted_path = path_from_gns.rstrip('/') if path_from_gns else ""

            assert_test_result(
                normalized_original_path == normalized_converted_path,
                f"GNS '{gns_valid}' 成功转换回路径: '{path_from_gns}' (与原始路径 '{remote_base_test_dir_path}' 匹配)",
                f"GNS '{gns_valid}' 转换路径失败或不匹配。得到: '{path_from_gns}', 期望: '{remote_base_test_dir_path}'",
                "GNS to Path (Valid)"
            )
        else:
            log('WARNING', "跳过 gns_to_path 有效 GNS 测试，因为前置的 path_to_gns 测试未获取到有效 GNS。")

        invalid_gns_for_test = "invalid-gns-string-123abcXYZ"
        log('INFO', f"    测试 gns_to_path 无效 GNS: {invalid_gns_for_test}")
        path_from_invalid_gns = client.gns_to_path(invalid_gns_for_test)
        assert_test_result(
            not path_from_invalid_gns,
            f"无效 GNS '{invalid_gns_for_test}' 转换路径按预期返回空字符串。",
            f"无效 GNS '{invalid_gns_for_test}' 转换路径意外返回: {path_from_invalid_gns}",
            "GNS to Path (Invalid GNS)"
        )

        log('INFO', "\n--- 创建本地测试文件 (为后续上传等操作准备) ---")
        local_small_file_path = os.path.join(local_upload_source_dir, small_file_name)
        create_dummy_file(local_small_file_path, small_file_size_bytes)
        local_large_file_path = os.path.join(local_upload_source_dir, large_file_name)
        create_dummy_file(local_large_file_path, large_file_size_bytes)

        local_batch_file1_path = os.path.join(local_upload_source_dir, batch_file1_name)
        create_dummy_file(local_batch_file1_path, batch_file_size)
        local_batch_file2_path = os.path.join(local_upload_source_dir, batch_file2_name)
        create_dummy_file(local_batch_file2_path, batch_file_size)
        local_batch_file3_path = os.path.join(local_upload_source_dir, batch_file3_name)
        create_dummy_file(local_batch_file3_path, batch_file_size)
        log('SUCCESS', "    本地测试文件创建完成。")

        log('INFO', "\n--- 3. 单文件上传测试 (小型文件) ---")
        remote_small_file_full_path = f"{remote_base_test_dir_path}/{small_file_name}"
        try:
            log('INFO', f"    上传小型文件: '{local_small_file_path}' -> '{remote_small_file_full_path}'")
            upload_result = client.upload_file(local_small_file_path, remote_small_file_full_path)
            assert_test_result(
                upload_result is not None and len(upload_result) == 3,
                f"小型文件上传调用成功。GNS: {upload_result[0] if upload_result else 'N/A'}",
                "小型文件上传调用失败或返回格式不正确。",
                "Small File Upload"
            )
            if upload_result:
                uploaded_files_remote_paths[small_file_name] = remote_small_file_full_path
        except Exception as e:
            log('ERROR', f"    小型文件上传失败: {e}")
            log('ERROR', traceback.format_exc())
            all_tests_passed_global = False

        log('INFO', "\n--- 4. 分片上传测试 (大型文件) ---")
        remote_large_file_full_path = f"{remote_base_test_dir_path}/{large_file_name}"
        try:
            log('INFO', f"    上传大型文件: '{local_large_file_path}' -> '{remote_large_file_full_path}'")
            upload_result_large = client.upload_file(local_large_file_path, remote_large_file_full_path)
            assert_test_result(
                upload_result_large is not None and len(upload_result_large) == 3,
                f"大型文件上传调用成功。GNS: {upload_result_large[0] if upload_result_large else 'N/A'}",
                "大型文件上传调用失败或返回格式不正确。",
                "Large File Upload"
            )
            if upload_result_large:
                uploaded_files_remote_paths[large_file_name] = remote_large_file_full_path
        except Exception as e:
            log('ERROR', f"    大型文件上传失败: {e}")
            log('ERROR', traceback.format_exc())
            all_tests_passed_global = False

        log('INFO', "\n--- 5. 单文件下载测试 ---")
        if small_file_name in uploaded_files_remote_paths:
            try:
                remote_path_to_download = uploaded_files_remote_paths[small_file_name]
                local_downloaded_small_file_path = os.path.join(local_download_target_dir, f"downloaded_{small_file_name}")
                log('INFO', f"    下载文件: '{remote_path_to_download}' -> '{local_downloaded_small_file_path}'")

                downloaded_path = client.download_file(remote_path_to_download, local_download_target_dir, local_filename_override=f"downloaded_{small_file_name}")
                assert_test_result(
                    downloaded_path is not None and os.path.exists(downloaded_path),
                    f"文件下载成功到: {downloaded_path}",
                    f"文件下载失败或目标路径 '{downloaded_path}' 未找到。",
                    "Small File Download"
                )

                if downloaded_path and os.path.exists(downloaded_path):
                    original_md5 = calculate_md5(local_small_file_path)
                    downloaded_md5 = calculate_md5(downloaded_path)
                    md5_match = original_md5 and downloaded_md5 and original_md5 == downloaded_md5
                    assert_test_result(
                        md5_match,
                        "MD5校验成功：下载的文件与原文件一致。",
                        f"MD5校验失败! 原文件MD5: {original_md5}, 下载文件MD5: {downloaded_md5}",
                        "Small File Download MD5 Check"
                    )
                else:
                    log('WARNING', "跳过下载文件MD5校验，因为下载步骤失败或文件未找到。")
            except Exception as e:
                log('ERROR', f"    文件下载失败: {e}")
                log('ERROR', traceback.format_exc())
                all_tests_passed_global = False
        else:
            log('WARNING', "    跳过小型文件下载测试，因为其上传未成功记录路径。")

        log('INFO', "\n--- 6. 批量下载测试 ---")
        batch_test_setup_ok = False
        remote_batch_subdir_full_path = f"{remote_base_test_dir_path}/{batch_subdir_name}"
        remote_batch_files_to_upload: List[Tuple[str, str, str]] = [
            (local_batch_file1_path, batch_file1_name, remote_base_test_dir_path),
            (local_batch_file2_path, batch_file2_name, remote_base_test_dir_path),
        ]
        remote_paths_for_batch_download: List[str] = []
        expected_zip_contents: List[str] = []

        try:
            log('INFO', "    6a. 准备批量下载所需的文件和目录...")
            for local_path, fname, remote_parent_path in remote_batch_files_to_upload:
                r_path = f"{remote_parent_path}/{fname}"
                client.upload_file(local_path, r_path)
                remote_paths_for_batch_download.append(r_path)
                expected_zip_contents.append(fname)
                log('DEBUG', f"      Uploaded for batch: {fname} to {r_path}")

            if client.create_directory(remote_batch_subdir_full_path):
                log('DEBUG', f"      远程批量测试子目录 '{remote_batch_subdir_full_path}' 创建成功。")
                remote_paths_for_batch_download.append(remote_batch_subdir_full_path)
                remote_batch_file3_full_path = f"{remote_batch_subdir_full_path}/{batch_file3_name}"
                client.upload_file(local_batch_file3_path, remote_batch_file3_full_path)
                log('DEBUG', f"      Uploaded for batch: {batch_file3_name} into {remote_batch_subdir_full_path}")
                expected_zip_contents.append(os.path.join(batch_subdir_name, batch_file3_name))
            else:
                raise Exception(f"创建远程批量测试子目录 '{remote_batch_subdir_full_path}' 失败。")
            batch_test_setup_ok = True
            log('SUCCESS', "    批量下载所需文件和目录准备完成。")
        except Exception as e:
            log('ERROR', f"    准备批量下载文件和目录失败: {e}")
            log('ERROR', traceback.format_exc())
            all_tests_passed_global = False

        if batch_test_setup_ok:
            try:
                batch_zip_name = f"batch_download_{timestamp_str}.zip"
                parsed_host_url = urlparse(client._config.host)
                reqhost = parsed_host_url.hostname
                if not reqhost:
                    log('WARNING', f"无法从配置的host '{client._config.host}' 解析reqhost，将尝试使用空字符串。这可能导致批量下载失败。")
                    reqhost = ""

                log('INFO', f"    6b. 执行批量下载。请求主机: '{reqhost}', 下载目标: {remote_paths_for_batch_download}")
                downloaded_zip_path = client.download_batch(
                    remote_paths=remote_paths_for_batch_download,
                    local_download_directory=local_download_target_dir,
                    zip_name=batch_zip_name,
                    reqhost=reqhost
                )
                assert_test_result(
                    downloaded_zip_path is not None and os.path.exists(downloaded_zip_path) and os.path.getsize(downloaded_zip_path) > 0,
                    f"批量下载的ZIP文件 '{downloaded_zip_path}' 成功下载且非空。",
                    f"批量下载的ZIP文件未找到、为空或API调用失败。路径: {downloaded_zip_path}",
                    "Batch Download Execution"
                )

                if downloaded_zip_path and os.path.exists(downloaded_zip_path) and os.path.getsize(downloaded_zip_path) > 0:
                    log('SUCCESS', f"    批量下载的ZIP文件 '{downloaded_zip_path}' 存在且非空。正在校验内容...")
                    with zipfile.ZipFile(downloaded_zip_path, 'r') as zf:
                        zip_actual_contents = zf.namelist()
                        log('DEBUG', f"      ZIP实际内容: {zip_actual_contents}")
                        log('DEBUG', f"      ZIP预期包含 (部分路径): {expected_zip_contents}")

                        all_expected_found = True
                        for expected_item in expected_zip_contents:
                            normalized_expected_item = expected_item.replace('\\', '/')
                            if not any(normalized_expected_item in zip_item.replace('\\', '/') for zip_item in zip_actual_contents):
                                log('ERROR', f"      预期项目 '{expected_item}' 未在ZIP文件中找到。")
                                all_expected_found = False

                        if all_expected_found:
                            log('SUCCESS', "    批量下载的ZIP文件内容校验通过!")
                        else:
                            log('ERROR', "    批量下载的ZIP文件内容校验失败。")
                            all_tests_passed_global = False
                else:
                    log('ERROR', "    批量下载的ZIP文件未找到或为空。")
                    all_tests_passed_global = False
            except Exception as e:
                log('ERROR', f"    批量下载执行失败: {e}")
                log('ERROR', traceback.format_exc())
                all_tests_passed_global = False
        else:
            log('WARNING', "    跳过批量下载执行，因为准备步骤失败。")

    except Exception as e_global:
        log('ERROR', f"测试过程中发生全局错误: {e_global}")
        log('ERROR', traceback.format_exc())
        all_tests_passed_global = False
    finally:
        log('INFO', "\n--- 7. 清理测试环境 ---")
        try:
            if os.path.exists(local_base_dir):
                shutil.rmtree(local_base_dir)
                log('INFO', f"    本地测试基础目录 '{local_base_dir}' 已删除。")
        except Exception as e_clean_local:
            log('WARNING', f"    清理本地测试目录 '{local_base_dir}' 失败: {e_clean_local}")

        if remote_base_test_dir_path and client._config.token_id: # Ensure client is logged in for cleanup
            try:
                log('INFO', f"    删除远程主测试目录: {remote_base_test_dir_path}")
                if client.delete_directory(remote_base_test_dir_path): # Assuming delete_directory is recursive for non-empty
                    log('SUCCESS', f"    远程主测试目录 '{remote_base_test_dir_path}' 删除成功。")
                else:
                    log('WARNING', f"    远程主测试目录 '{remote_base_test_dir_path}' 删除失败或未完全成功 (API返回False)。可能需要手动清理。")
                    # Not marking as test failure, but it's a cleanup issue.
            except Exception as e_clean_remote:
                log('WARNING', f"    清理远程测试目录 '{remote_base_test_dir_path}' 过程中发生错误: {e_clean_remote}")
        else:
            log('INFO', "    无需清理远程测试目录 (未创建或未登录)。")

        # if client._config.token_id: # Logout if logged in
        #     client.logout()
        #     log('INFO', "已注销登录。")
        log('INFO', f"--- AnyshareClient 文件传输功能测试结束。总体结果: {'成功' if all_tests_passed_global else '失败'} ---")
        if not all_tests_passed_global:
            sys.exit(1)
