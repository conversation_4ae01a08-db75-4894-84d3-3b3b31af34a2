#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到 Python 路径
anyshare_client_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, anyshare_client_root)

from anyshare_client import DirectoryApi, Config
from anyshare_client.api.models import *
import time
import traceback
from typing import Callable, Optional

def run_directory_tests(config: Config, directory_api: DirectoryApi, parent_doc_id: str, log: Callable):
    log('INFO', f"--- 开始目录 API 测试 (将在父文档库 ID: {parent_doc_id} 中进行操作) ---")

    timestamp_str = time.strftime("%Y%m%d_%H%M%S")
    main_test_dir_name = f"sdk_test_suite_main_{timestamp_str}"
    main_test_dir_docid = None

    sub_dir_name_lvl1_original = f"level1_subdir_{timestamp_str}"
    sub_dir_name_lvl1_current = sub_dir_name_lvl1_original
    sub_dir_docid_lvl1 = None
    multilevel_path = f"multi_{timestamp_str}/level/path_test"
    created_multilevel_leaf_docid = None
    tag_to_test = f"sdk_tag_{timestamp_str}"
    test_succeeded_overall = True

    try:
        log('INFO', f"0. 在 {parent_doc_id} 下创建主测试目录 '{main_test_dir_name}':")
        create_main_req = DirCreateReq(docid=parent_doc_id, name=main_test_dir_name, ondup=2)
        create_main_res: Optional[DirCreateRes] = directory_api.create_directory(create_main_req)
        if create_main_res and create_main_res.docid:
            main_test_dir_docid = create_main_res.docid
            main_test_dir_name = create_main_res.name
            log('SUCCESS', f"  主测试目录 '{main_test_dir_name}' 创建成功: ID: {main_test_dir_docid}")
            log('DEBUG', f"    响应详情: Rev: {create_main_res.rev}, Modified: {create_main_res.modified}, Creator: {create_main_res.creator}, Editor: {create_main_res.editor}")
        else:
            log('ERROR', f"  主测试目录创建失败。响应: {create_main_res}")
            test_succeeded_overall = False
            raise Exception("未能创建主测试目录，后续测试无法进行。")

        log('INFO', f"1. 列出主测试目录 '{main_test_dir_name}' ({main_test_dir_docid}) 内容:")
        list_req = DirListReq(docid=main_test_dir_docid, attr=True, by="name", sort="asc")
        list_res: Optional[DirListRes] = directory_api.list_directory_contents(list_req)
        if list_res:
            log('SUCCESS', f"  列出成功: 包含 {len(list_res.dirs)} 个目录, {len(list_res.files)} 个文件")
            if not list_res.dirs and not list_res.files:
                log('INFO', "    目录为空，符合预期。")
            for d_item in list_res.dirs: log('DEBUG', f"    Dir: {d_item.name}, ID: {d_item.docid}")
            for f_item in list_res.files: log('DEBUG', f"    File: {f_item.name}, ID: {f_item.docid}")
        else:
            log('ERROR', f"  列出主测试目录失败或目录返回非预期结构。响应: {list_res}")
            test_succeeded_overall = False

        log('INFO', f"2. 在 '{main_test_dir_name}' 内创建子目录 '{sub_dir_name_lvl1_current}':")
        create_sub_req = DirCreateReq(docid=main_test_dir_docid, name=sub_dir_name_lvl1_current, ondup=1)
        create_sub_res: Optional[DirCreateRes] = directory_api.create_directory(create_sub_req)
        if create_sub_res and create_sub_res.docid:
            sub_dir_docid_lvl1 = create_sub_res.docid
            sub_dir_name_lvl1_current = create_sub_res.name
            log('SUCCESS', f"  子目录 '{sub_dir_name_lvl1_current}' 创建成功: ID: {sub_dir_docid_lvl1}")
        else:
            log('ERROR', f"  子目录 '{sub_dir_name_lvl1_current}' 创建失败。响应: {create_sub_res}")
            test_succeeded_overall = False

        if sub_dir_docid_lvl1:
            log('INFO', f"3. 获取子目录 '{sub_dir_name_lvl1_current}' ({sub_dir_docid_lvl1}) 属性:")
            attr_req = DirAttributeReq(docid=sub_dir_docid_lvl1)
            attr_res: Optional[DirAttributeRes] = directory_api.get_directory_attributes(attr_req)
            if attr_res:
                log('SUCCESS', f"  获取属性成功.")
                log('DEBUG', f"    属性详情: {attr_res.model_dump()}")
            else:
                log('ERROR', f"  获取属性失败。响应: {attr_res}")
                test_succeeded_overall = False

            renamed_target_name = f"{sub_dir_name_lvl1_original}_renamed"
            log('INFO', f"4. 重命名子目录 '{sub_dir_name_lvl1_current}' ({sub_dir_docid_lvl1}) 为 '{renamed_target_name}':")
            rename_req = DirRenameReq(docid=sub_dir_docid_lvl1, name=renamed_target_name, ondup=2)
            rename_res: Optional[DirRenameRes] = directory_api.rename_directory(rename_req)
            if rename_res and rename_res.name:
                sub_dir_name_lvl1_current = rename_res.name
                log('SUCCESS', f"  重命名成功。新名称: '{sub_dir_name_lvl1_current}'")
            elif rename_res is True:
                log('WARNING', f"  重命名请求成功但未返回结构化名称，假设为 '{renamed_target_name}' (ondup=2 behavior)")
                sub_dir_name_lvl1_current = renamed_target_name
            elif rename_res and rename_res.name is None:
                 log('WARNING', f" 重命名请求返回了模型但新名称为None。响应: {rename_res}，原目标名: {renamed_target_name}")
            else:
                log('ERROR', f"  重命名失败或响应不明确。响应: {rename_res}")
                test_succeeded_overall = False
        else:
            log('WARNING', "跳过获取属性和重命名测试，因为前置子目录创建失败。")

        log('INFO', f"5. 在 '{main_test_dir_name}' ({main_test_dir_docid}) 内创建多级目录 '{multilevel_path}':")
        create_multi_req = DirCreatemultileveldirReq(docid=main_test_dir_docid, path=multilevel_path)
        create_multi_res: Optional[DirCreatemultileveldirRes] = directory_api.create_multilevel_directory(create_multi_req)
        if create_multi_res and create_multi_res.docid:
            created_multilevel_leaf_docid = create_multi_res.docid
            log('SUCCESS', f"  多级目录创建成功: 最底层ID: {created_multilevel_leaf_docid}")
            log('DEBUG', f"    响应详情: Rev: {create_multi_res.rev}, Modified: {create_multi_res.modified}")
        else:
            log('ERROR', f"  多级目录创建失败。响应: {create_multi_res}")
            test_succeeded_overall = False

        if sub_dir_docid_lvl1:
            log('INFO', f"6. 测试目录标签操作于 '{sub_dir_name_lvl1_current}' ({sub_dir_docid_lvl1}):")
            log('INFO', f"  6a. 添加标签 '{tag_to_test}':")
            add_tag_req = DirAddtagReq(docid=sub_dir_docid_lvl1, tag=tag_to_test)
            add_tag_success = directory_api.add_directory_tag(add_tag_req)
            log('SUCCESS' if add_tag_success else 'ERROR', f"    添加标签 '{tag_to_test}' 请求结果: {add_tag_success}")
            if not add_tag_success: test_succeeded_overall = False

            log('INFO', f"    6b. 验证标签添加:")
            attr_res_after_add: Optional[DirAttributeRes] = directory_api.get_directory_attributes(DirAttributeReq(docid=sub_dir_docid_lvl1))
            if attr_res_after_add and isinstance(attr_res_after_add.tags, list) and tag_to_test in attr_res_after_add.tags:
                log('SUCCESS', f"    验证成功: 标签 '{tag_to_test}' 存在于 {attr_res_after_add.tags}")
            elif attr_res_after_add:
                log('ERROR', f"    验证失败: 标签 '{tag_to_test}' 未找到。当前标签: {attr_res_after_add.tags}")
                test_succeeded_overall = False
            else:
                log('ERROR', f"    验证失败: 获取属性用于验证标签时失败。响应: {attr_res_after_add}")
                test_succeeded_overall = False

            log('INFO', f"  6c. 删除标签 '{tag_to_test}':")
            del_tag_req = DirDeletetagReq(docid=sub_dir_docid_lvl1, tag=tag_to_test)
            del_tag_success = directory_api.delete_directory_tag(del_tag_req)
            log('SUCCESS' if del_tag_success else 'ERROR', f"    删除标签 '{tag_to_test}' 请求结果: {del_tag_success}")
            if not del_tag_success: test_succeeded_overall = False

            log('INFO', f"    6d. 验证标签删除:")
            attr_res_after_del: Optional[DirAttributeRes] = directory_api.get_directory_attributes(DirAttributeReq(docid=sub_dir_docid_lvl1))
            if attr_res_after_del and isinstance(attr_res_after_del.tags, list) and tag_to_test not in attr_res_after_del.tags:
                log('SUCCESS', f"    验证成功: 标签 '{tag_to_test}' 已不存在于 {attr_res_after_del.tags}")
            elif attr_res_after_del:
                 log('ERROR', f"    验证失败: 标签 '{tag_to_test}' 仍然存在。当前标签: {attr_res_after_del.tags}")
                 test_succeeded_overall = False
            else:
                log('ERROR', f"    验证失败: 获取属性用于验证标签删除时失败。响应: {attr_res_after_del}")
                test_succeeded_overall = False
        else:
            log('WARNING', f"跳过目录标签测试，因为子目录 '{sub_dir_name_lvl1_current}' 未成功创建或ID未知。")

        log('INFO', f"7. 获取主测试目录 '{main_test_dir_name}' ({main_test_dir_docid}) 大小:")
        size_req = DirSizeReq(docid=main_test_dir_docid)
        size_res: Optional[DirSizeRes] = directory_api.get_directory_size(size_req)
        if size_res:
            log('SUCCESS', f"  获取大小成功: 目录数: {size_res.dirnum}, 文件数: {size_res.filenum}, 总大小: {size_res.totalsize} bytes, 回收站大小: {size_res.recyclesize} bytes")
        else:
            log('ERROR', f"  获取大小失败。响应: {size_res}")
            test_succeeded_overall = False

    except Exception as e:
        log('ERROR', f"---!!! 目录 API 测试中发生严重错误: {e} !!!---")
        log('ERROR', traceback.format_exc())
        test_succeeded_overall = False

    finally:
        log('INFO', "--- 清理阶段 (目录测试) ---")
        dirs_to_delete_in_order = []
        if sub_dir_docid_lvl1:
            dirs_to_delete_in_order.append((sub_dir_name_lvl1_current, sub_dir_docid_lvl1))
        if created_multilevel_leaf_docid:
            dirs_to_delete_in_order.append((multilevel_path.split('/')[-1], created_multilevel_leaf_docid))

        for dir_name, dir_id_to_delete in dirs_to_delete_in_order:
            if dir_id_to_delete:
                log('INFO', f"  尝试删除子条目: '{dir_name}' ({dir_id_to_delete})")
                delete_req = DirDeleteReq(docid=dir_id_to_delete)
                delete_res: Optional[DirDeleteRes] = directory_api.delete_directory(delete_req)
                if delete_res and delete_res.isdirexist is False:
                    log('SUCCESS', f"    '{dir_name}' 删除成功。")
                elif delete_res:
                    log('WARNING', f"    '{dir_name}' 删除请求后目录仍存在? isdirexist: {delete_res.isdirexist}. 响应: {delete_res}")
                else:
                    log('ERROR', f"    '{dir_name}' 删除失败或无响应。响应: {delete_res}")

        if main_test_dir_docid:
            log('INFO', f"  尝试删除主测试目录: '{main_test_dir_name}' ({main_test_dir_docid})")
            delete_main_req = DirDeleteReq(docid=main_test_dir_docid)
            delete_main_res: Optional[DirDeleteRes] = directory_api.delete_directory(delete_main_req)
            if delete_main_res and delete_main_res.isdirexist is False:
                log('SUCCESS', f"  主测试目录 '{main_test_dir_name}' 删除成功。")
            elif delete_main_res:
                log('WARNING', f"  主测试目录 '{main_test_dir_name}' 删除请求后目录仍存在? isdirexist: {delete_main_res.isdirexist}. 响应: {delete_main_res}")
            else:
                log('ERROR', f"  主测试目录 '{main_test_dir_name}' 删除失败或无响应。响应: {delete_main_res}")
        elif not dirs_to_delete_in_order:
             log('INFO',"主测试目录未创建或ID未知，且无其他已知目录ID需要清理。")

        log('INFO', f"--- 目录 API 测试结束 (总体结果: {'成功' if test_succeeded_overall else '部分失败或完全失败'}) ---")
        return test_succeeded_overall

if __name__ == '__main__':
    from anyshare_client.api.models import Entrydoc2GetReq
    from anyshare_client import EntryDocApi
    import datetime

    def local_log(level: str = 'INFO',message: str = '') -> None:
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        print(f"[{timestamp}] [{level}] {message}")

    local_log('INFO', "本地运行 test_anyshare_directory.py 进行测试...")

    test_config = Config(host="http://files.flexiv.cloud", port=88, base_url="/api/v1")
    test_config.token_id = "token_id"
    test_config.user_id = "user_id"

    if test_config.token_id == "YOUR_VALID_TOKEN_ID" or not test_config.user_id:
        local_log('ERROR', "请为此本地测试提供有效的 token_id 和 user_id")
        exit()

    entry_doc_api_local = EntryDocApi(test_config)
    managed_libs_response = entry_doc_api_local.get_managed_doc_libs()
    parent_id_local = None

    if managed_libs_response and hasattr(managed_libs_response, 'docinfos') and managed_libs_response.docinfos:
        parent_id_local = managed_libs_response.docinfos[0].docid
    elif isinstance(managed_libs_response, dict) and managed_libs_response.get("docinfos"):
         parent_id_local = managed_libs_response["docinfos"][0].get("docid")
    elif isinstance(managed_libs_response, list) and len(managed_libs_response) > 0 and isinstance(managed_libs_response[0], dict):
        parent_id_local = managed_libs_response[0].get("docid")

    if parent_id_local:
        local_log('INFO', f"获取到本地测试父目录 ID: {parent_id_local}")
        dir_api_local = DirectoryApi(test_config)
        run_directory_tests(test_config, dir_api_local, parent_id_local, local_log)
    else:
        local_log('ERROR', f"本地测试无法获取父目录ID，测试中止。响应: {managed_libs_response}")


