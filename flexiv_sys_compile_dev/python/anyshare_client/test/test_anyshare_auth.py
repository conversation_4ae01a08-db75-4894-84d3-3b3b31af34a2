#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from anyshare_client import AuthApi, Config
from typing import Callable
import traceback

def run_auth_tests(config: Config, auth_api: AuthApi, log: Callable) -> bool:
    """
    运行 Auth API 相关测试 (目前主要是可选的登出测试)
    返回 True 如果测试通过或无测试执行，False 如果发生错误。
    """
    log('INFO', "--- 开始 Auth API 测试 ---")
    test_succeeded_overall = True

    try:
        # 1. 登录测试 (如果启用)
        # 注意：主脚本通常应处理登录或确保 token 已设置。
        # 如果需要在此处测试登录，需要传入用户名/密码。
        # log('INFO', "1. 测试登录 (如果启用)")
        # if hasattr(config, 'test_username') and hasattr(config, 'test_password'):
        #     login_successful = auth_api.login(config.test_username, config.test_password)
        #     log('SUCCESS' if login_successful else 'ERROR', f"  登录测试结果: {login_successful}")
        #     if not login_successful: test_succeeded_overall = False
        # else:
        #     log('INFO', "  跳过登录测试，未提供用户名/密码配置。")
        pass # Placeholder if no explicit auth tests are run besides logout

        # 2. 登出测试 (可选, 通常在所有其他测试之后执行)
        # log('INFO', "2. 测试登出 (在主脚本的末尾执行可能更合适)")
        # 注意：在实际测试流程中，登出通常是最后一步，以使 token 失效。
        # 将登出逻辑放在这里可能会影响后续依赖 token 的测试。
        # logout_res = auth_api.logout()
        # log('SUCCESS' if logout_res else 'ERROR', f"  登出测试结果: {logout_res}")
        # if not logout_res: test_succeeded_overall = False

    except Exception as e:
        log('ERROR', f"---!!! Auth API 测试中发生严重错误: {e} !!!---")
        log('ERROR', traceback.format_exc())
        test_succeeded_overall = False

    log('INFO', f"--- Auth API 测试结束 (总体结果: {'成功' if test_succeeded_overall else '失败'}) ---")
    return test_succeeded_overall

# 本地测试逻辑
if __name__ == '__main__':
    import datetime # For local log
    def local_log(level: str = 'INFO',message: str = '') -> None:
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        print(f"[{timestamp}] [{level}] {message}")

    local_log('INFO', "本地运行 test_anyshare_auth.py 进行测试...")
    test_config = Config(host="http://files.flexiv.cloud", port=88, base_url="/api/v1")
    # 对于认证测试，本地测试可能需要模拟登录或直接测试登出（如果token有效）
    test_config.token_id = "token_id"
    test_config.user_id = "user_id"
    test_config.token_id_expires = 3600


    if not test_config.token_id:
         local_log('WARNING', "未提供 Token ID，Auth 测试可能受限。")

    auth_api_local = AuthApi(test_config)
    login_res = auth_api_local.login("zhihao.si", "FX20250322szh!")
    local_log('SUCCESS' if login_res else 'ERROR', f"  登录测试结果: {login_res}")
    run_auth_tests(test_config, auth_api_local, local_log)