#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from anyshare_client import EntryDocApi, Config
from anyshare_client.api.models import *
from typing import Callable, Optional

def run_entrydoc_tests(config: Config, entry_doc_api: EntryDocApi, log: Callable) -> Optional[str]:
    """
    运行 EntryDoc API 相关测试, 并返回第一个找到的可管理文档库 ID (如果存在)
    """
    log('INFO', "--- 开始 EntryDoc API 测试 ---")
    parent_doc_id_for_directory_tests = None
    test_succeeded_overall = True

    try:
        # 1. 获取可管理的文档库列表
        log('INFO', "1. 获取管理的文档库列表 (get_managed_doc_libs)")
        managed_doc_libs_response = entry_doc_api.get_managed_doc_libs()

        if isinstance(managed_doc_libs_response, Entrydoc2GetmanagedRes):
            log('SUCCESS', f"  获取管理文档库成功，包含 {len(managed_doc_libs_response.docinfos)} 个条目。")
            if managed_doc_libs_response.docinfos:
                log('INFO', "  管理的文档库详情：")
                for doc_info in managed_doc_libs_response.docinfos:
                    current_doc_id = doc_info.docid
                    current_doc_name = doc_info.name
                    log('DEBUG', f"    名称: {current_doc_name}, ID: {current_doc_id}, 类型: {doc_info.typename}")
                    if not parent_doc_id_for_directory_tests:
                         parent_doc_id_for_directory_tests = current_doc_id
                         log('INFO', f"    >> 将使用此文档库作为后续目录测试的父目录: {current_doc_name} ({parent_doc_id_for_directory_tests})")
            else:
                 log('INFO', "  管理文档库列表为空。")
        elif isinstance(managed_doc_libs_response, Error):
            log('ERROR', f"  获取管理文档库失败: {managed_doc_libs_response.errmsg} (代码: {managed_doc_libs_response.errcode})")
            test_succeeded_overall = False
        elif isinstance(managed_doc_libs_response, str):
            log('ERROR', f"  获取管理文档库失败: 服务器返回非结构化错误: {managed_doc_libs_response}")
            test_succeeded_overall = False
        elif managed_doc_libs_response is None:
            log('ERROR', "  获取管理文档库失败: 未收到响应 (None)。")
            test_succeeded_overall = False
        else:
            log('ERROR', f"  获取管理文档库失败: 未知响应类型: {type(managed_doc_libs_response)} 内容: {managed_doc_libs_response}")
            test_succeeded_overall = False

        # 2. 获取可访问的文档库列表 (可选，作为对比)
        log('INFO', "2. 获取可访问的文档库列表 (get_accessible_doc_libs)")
        get_libs_req = Entrydoc2GetReq(doctype=0)
        accessible_doc_libs_response = entry_doc_api.get_accessible_doc_libs(get_libs_req)
        if isinstance(accessible_doc_libs_response, Entrydoc2GetRes):
             log('SUCCESS', f"  获取可访问文档库成功，包含 {len(accessible_doc_libs_response.docinfos)} 个条目。")
             # 可以添加更多日志记录 accessible_doc_libs 的内容
        elif isinstance(accessible_doc_libs_response, Error):
            log('ERROR', f"  获取可访问文档库失败: {accessible_doc_libs_response.errmsg} (代码: {accessible_doc_libs_response.errcode})")
            test_succeeded_overall = False
        elif isinstance(accessible_doc_libs_response, str):
            log('ERROR', f"  获取可访问文档库失败: 服务器返回非结构化错误: {accessible_doc_libs_response}")
            test_succeeded_overall = False
        elif accessible_doc_libs_response is None:
            log('ERROR', "  获取可访问文档库失败: 未收到响应 (None)。")
            test_succeeded_overall = False
        else:
             log('ERROR', f"  获取可访问文档库失败: 未知响应类型: {type(accessible_doc_libs_response)} 内容: {accessible_doc_libs_response}")
             test_succeeded_overall = False

        # 3. 如果找到了父目录ID，测试获取其配额和信息
        if parent_doc_id_for_directory_tests:
            log('INFO', f"3. 获取选定父目录 ({parent_doc_id_for_directory_tests}) 的配额和信息")
            # 3a. 获取配额
            get_quota_req = Entrydoc2GetdocquotaReq(docid=parent_doc_id_for_directory_tests)
            doc_lib_quota_response = entry_doc_api.get_doc_lib_quota(get_quota_req)
            if isinstance(doc_lib_quota_response, Entrydoc2GetdocquotaRes):
                 log('SUCCESS', f"  获取配额成功.")
                 log('DEBUG', f"    配额详情: quota={doc_lib_quota_response.quota}, used={doc_lib_quota_response.used}")
            elif isinstance(doc_lib_quota_response, Error):
                 log('ERROR', f"  获取配额失败: {doc_lib_quota_response.errmsg} (代码: {doc_lib_quota_response.errcode})")
                 test_succeeded_overall = False
            elif isinstance(doc_lib_quota_response, str):
                 log('ERROR', f"  获取配额失败: 服务器返回非结构化错误: {doc_lib_quota_response}")
                 test_succeeded_overall = False
            elif doc_lib_quota_response is None:
                 log('ERROR', "  获取配额失败: 未收到响应 (None)。")
                 test_succeeded_overall = False
            else:
                 log('ERROR', f"  获取配额失败: 未知响应类型. 响应: {doc_lib_quota_response}")
                 test_succeeded_overall = False
            # 3b. 获取信息
            get_info_req = Entrydoc2GetdocinfoReq(docid=parent_doc_id_for_directory_tests)
            doc_lib_info_response = entry_doc_api.get_doc_lib_info(get_info_req)
            if isinstance(doc_lib_info_response, Entrydoc2GetdocinfoRes):
                log('SUCCESS', f"  获取文档库信息成功.")
                log('DEBUG', f"    信息详情: doctype={doc_lib_info_response.doctype}")
            elif isinstance(doc_lib_info_response, Error):
                log('ERROR', f"  获取文档库信息失败: {doc_lib_info_response.errmsg} (代码: {doc_lib_info_response.errcode})")
                test_succeeded_overall = False
            elif isinstance(doc_lib_info_response, str):
                log('ERROR', f"  获取文档库信息失败: 服务器返回非结构化错误: {doc_lib_info_response}")
                test_succeeded_overall = False
            elif doc_lib_info_response is None:
                log('ERROR', "  获取文档库信息失败: 未收到响应 (None)。")
                test_succeeded_overall = False
            else:
                log('ERROR', f"  获取文档库信息失败: 未知响应类型. 响应: {doc_lib_info_response}")
                test_succeeded_overall = False
        else:
             log('WARNING', "未找到父目录ID，跳过获取配额和信息的测试。")

    except Exception as e:
        log('ERROR', f"---!!! EntryDoc API 测试中发生严重错误: {e} !!!---")
        import traceback
        log('ERROR', traceback.format_exc())
        test_succeeded_overall = False
        parent_doc_id_for_directory_tests = None # Ensure we don't return an invalid ID on error

    log('INFO', f"--- EntryDoc API 测试结束 (总体结果: {'成功' if test_succeeded_overall else '部分失败或完全失败'}) ---")
    return parent_doc_id_for_directory_tests

# 本地测试逻辑
if __name__ == '__main__':
    import datetime # For local log
    from anyshare_client import Config # For local config
    def local_log(level: str = 'INFO',message: str = '') -> None:
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        print(f"[{timestamp}] [{level}] {message}")

    local_log('INFO', "本地运行 test_anyshare_entrydoc.py 进行测试...")
    test_config = Config(host="http://files.flexiv.cloud", port=88, base_url="/api/v1")
    test_config.token_id = "token_id" # 使用有效 Token
    test_config.user_id = "user_id"    # 使用有效 User ID

    if test_config.token_id == "YOUR_VALID_TOKEN_ID" or not test_config.user_id:
        local_log('ERROR', "请为此本地测试提供有效的 token_id 和 user_id")
        exit()

    entry_doc_api_local = EntryDocApi(test_config)
    returned_parent_id = run_entrydoc_tests(test_config, entry_doc_api_local, local_log)
    local_log('INFO', f"测试运行结束，返回的父目录 ID: {returned_parent_id}")