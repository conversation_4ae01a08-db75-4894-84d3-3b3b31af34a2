#!/usr/bin/env python3

import base64
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
from typing import Dict, List, Optional, Tuple, Callable, Any, Union

import os
import time
import math
import requests
from urllib.parse import urljoin, urlparse, urlunparse
import urllib3
import logging

from .config import Config
from .api import (
    AuthApi,
    EntryDocA<PERSON>,
    DirectoryApi,
    AutolockApi,
    FileTransferApi,
    FileOperationApi)
from .api.models import *
from .api.models.common_models import AnyshareApiException

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# --- Constants for File Transfer ---
_SINGLE_UPLOAD_MAX_BYTES = 5 * 1024 * 1024 * 1024  # 5 GiB
_DEFAULT_MULTIPART_PART_SIZE = 10 * 1024 * 1024    # 10 MB
_MIN_MULTIPART_PART_SIZE_S3_LIKE = 5 * 1024 * 1024 # 5MB (Typical S3 minimum)
_MAX_BATCH_DOWNLOAD_REDIRECTS = 5
_DEFAULT_CHUNK_SIZE = 8192

anyshare_client_logger = logging.getLogger(__name__)
anyshare_client_logger.setLevel(logging.INFO)

class AnyshareClient:
    _SUCCESS_NO_CONTENT = object()

    def __init__(self):
        self._config = Config()
        self._api = {
            'auth_api': AuthApi(self._config),
            'entrydoc_api': EntryDocApi(self._config),
            'directory_api': DirectoryApi(self._config),
            'autolock_api': AutolockApi(self._config),
            'file_operation_api': FileOperationApi(self._config),
            'file_transfer_api': FileTransferApi(self._config)
        }
        self._requests_verify_ssl = False # For direct OSS calls via requests library
        anyshare_client_logger.debug("AnyshareClient initialized.")
        
    def _encrypt_text(self, text: str) -> str:
        anyshare_client_logger.debug("Starting password encryption.")
        try:
            key_data = (
                "-----BEGIN PUBLIC KEY-----\n"
                "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7JL0DcaMUHumSdhxXTxqiABBC\n"
                "DERhRJIsAPB++zx1INgSEKPGbexDt1ojcNAc0fI+G/yTuQcgH1EW8posgUni0mcT\n"
                "E6CnjkVbv8ILgCuhy+4eu+2lApDwQPD9Tr6J8k21Ruu2sWV5Z1VRuQFqGm/c5vaT\n"
                "OQE5VFOIXPVTaa25mQIDAQAB\n"
                "-----END PUBLIC KEY-----"
            )
            key = RSA.importKey(key_data)
            cipher = PKCS1_v1_5.new(key)
            encrypted_bytes = cipher.encrypt(text.encode('utf-8'))
            encrypted_text = base64.b64encode(encrypted_bytes).decode('utf-8')
            anyshare_client_logger.debug("Password encryption successful.")
            return encrypted_text
        except Exception as e:
            anyshare_client_logger.error("Password encryption failed.", exc_info=True)
            raise AnyshareApiException(
                message="Password encryption failed.",
                location="AnyshareClient._encrypt_text",
                original_exception=e
            ) from e

    def _handle_api_response(self, response_union: Optional[Union[Any, Error, str]],
                              success_model_type: type) -> Any:
        anyshare_client_logger.debug(f"Handling API response. Expected success type: {success_model_type}, Actual response type: {type(response_union)}, Response (first 100 chars): {str(response_union)[:100]}")
        if success_model_type is type(None):
            if response_union is None:
                anyshare_client_logger.debug("API response is None and None was expected. Returning _SUCCESS_NO_CONTENT.")
                return AnyshareClient._SUCCESS_NO_CONTENT
    
        if isinstance(response_union, success_model_type):
            anyshare_client_logger.debug(f"API response matches expected success type {success_model_type}.")
            return response_union
        
        if isinstance(response_union, Error):
            msg = getattr(response_union, 'message', getattr(response_union, 'errmsg', 'Unknown error message'))
            code = getattr(response_union, 'error_code', getattr(response_union, 'errcode', 'UNKNOWN_CODE'))
            anyshare_client_logger.error(f"API returned an error: Code={code}, Message='{msg}'. Raw error object: {response_union}")
            raise AnyshareApiException(
                message=f"AnyShare API error: {msg}",
                location="AnyshareClient._handle_api_response",
                error_code=code,
                details=f"Raw error object: {response_union}"
            )
        if isinstance(response_union, str):
            raise AnyshareApiException(
                message=f"AnyShare API returned an unexpected error string.",
                location="AnyshareClient._handle_api_response",
                details=f"Error string: {response_union}"
            )
        
        if success_model_type is type(None) and response_union is not None:
             anyshare_client_logger.error(f"Unexpected response content from AnyShare API. Expected no content (None), but got {type(response_union)}.")
             raise AnyshareApiException(
                message=f"Unexpected response content from AnyShare API.",
                location="AnyshareClient._handle_api_response",
                details=f"Expected no content (None), but got {type(response_union)}."
            )

        if response_union is None:
            raise AnyshareApiException(
                message=f"AnyShare API returned None when {success_model_type} was expected.",
                location="AnyshareClient._handle_api_response"
            )

        raise AnyshareApiException(
            message=f"Unexpected response type from AnyShare API.",
            location="AnyshareClient._handle_api_response",
            details=f"Got {type(response_union)}, expected {success_model_type}, Error, or str."
        )

    def _call_api(self, api_name: str, method_name: str, req_data: Any, 
                  success_model_type: type, operation_description: str) -> Optional[Any]:
        anyshare_client_logger.debug(f"Calling API: {api_name}.{method_name} for '{operation_description}'. Request data (type: {type(req_data)}): {str(req_data)[:200]}...") # Log only first 200 chars of req_data
        try:
            if api_name != "auth_api" and not self._config.token_id:
                anyshare_client_logger.error(f"Operation '{operation_description}' failed: Not logged in (token_id is not set). API: {api_name}.{method_name}")
                raise AnyshareApiException(
                    message=f"Operation '{operation_description}' failed: Not logged in.",
                    location="AnyshareClient._call_api"
                )

            api_object = self._api.get(api_name)
            if not api_object:
                anyshare_client_logger.error(f"API group '{api_name}' not found for operation '{operation_description}'. Method: {method_name}")
                raise AnyshareApiException(
                    message=f"API group '{api_name}' not found for operation '{operation_description}'.",
                    location="AnyshareClient._call_api"
                )

            api_method_to_call = getattr(api_object, method_name, None)
            if not api_method_to_call:
                anyshare_client_logger.error(f"Method '{method_name}' not found in API group '{api_name}' for operation '{operation_description}'.")
                raise AnyshareApiException(
                    message=f"Method '{method_name}' not found in API group '{api_name}' for operation '{operation_description}'.",
                    location="AnyshareClient._call_api"
                )

            anyshare_client_logger.debug(f"Executing API method {api_name}.{method_name}.")
            response_union = api_method_to_call(req_data)
            anyshare_client_logger.debug(f"Raw response from {api_name}.{method_name} (type: {type(response_union)}): {str(response_union)[:200]}...") # Log only first 200 chars
            return self._handle_api_response(response_union, success_model_type)
        
        except AnyshareApiException as e:
            anyshare_client_logger.error(f"Caught AnyshareApiException during API call for '{operation_description}' ({api_name}.{method_name}): {e.message}", exc_info=False) # exc_info=False as it's already chained
            raise AnyshareApiException( # Re-wrap to ensure the location is this _call_api
                message=f"API call for '{operation_description}' failed: {e}",
                location="AnyshareClient._call_api",
                original_exception=e
            ) from e
        except ValueError as e: # Often Pydantic validation errors or similar
            anyshare_client_logger.error(f"Caught ValueError during API call for '{operation_description}' ({api_name}.{method_name}): {e}", exc_info=True)
            raise AnyshareApiException(
                message=f"API call for '{operation_description}' failed due to ValueError: {e}",
                location="AnyshareClient._call_api",
                original_exception=e
            ) from e
        except TypeError as e: # Can happen with unexpected response structures before Pydantic parsing
            anyshare_client_logger.error(f"Caught TypeError (likely unexpected response type/structure) during API call for '{operation_description}' ({api_name}.{method_name}): {e}", exc_info=True)
            raise AnyshareApiException(
                message=f"API call for '{operation_description}' failed due to unexpected response type: {e}",
                location="AnyshareClient._call_api",
                original_exception=e
            ) from e
        except Exception as e: # Catch-all for other unexpected issues
            anyshare_client_logger.error(f"Caught unexpected Exception during API call for '{operation_description}': {e}", exc_info=True)
            raise AnyshareApiException(
                message=f"Unexpected exception during API call for '{operation_description}': {e}",
                location="AnyshareClient._call_api",
                original_exception=e
            ) from e

    def _ensure_logged_in(self, operation_description: str) -> bool:
        """Helper to check login status. Raises AnyshareApiException if not logged in."""
        anyshare_client_logger.debug(f"Ensuring user is logged in for operation: '{operation_description}'. Current token_id set: {bool(self._config.token_id)}")
        if not self._config.token_id:
            anyshare_client_logger.error(f"Operation '{operation_description}' cannot proceed: User is not logged in (token_id is missing).")
            raise AnyshareApiException(
                message=f"{operation_description} failed: Not logged in.",
                location="AnyshareClient._ensure_logged_in"
            )
        anyshare_client_logger.debug("User is logged in.")
        return True

    # --- Path Helper Methods ---
    def _get_path_segment_for_api(self, remote_path: str) -> str:
        anyshare_client_logger.debug(f"Converting remote path '{remote_path}' to API path segment.")
        if not remote_path.startswith("AnyShare://"):
            anyshare_client_logger.warning(f"Path '{remote_path}' provided to _get_path_segment_for_api does not start with AnyShare://. Using as is for API segment.")
            return remote_path 
        
        namepath_segment = remote_path[len("AnyShare://"):]
        anyshare_client_logger.debug(f"API path segment for '{remote_path}': '{namepath_segment}'.")
        return namepath_segment

    def _extract_anyshare_path_components(self, remote_path: str, is_file_target: bool = False) -> Tuple[str, str]:
        anyshare_client_logger.debug(f"Extracting components from AnyShare path: '{remote_path}', is_file_target: {is_file_target}")
        if not remote_path or not remote_path.startswith("AnyShare://"):
            anyshare_client_logger.error(f"Invalid AnyShare path: '{remote_path}'. Must start with 'AnyShare://'.")
            raise AnyshareApiException(
                message=f"Invalid AnyShare path: '{remote_path}'. Must start with 'AnyShare://'.",
                location="AnyshareClient._extract_anyshare_path_components"
            )

        path_after_prefix = remote_path[len("AnyShare://"):].strip('/')
        anyshare_client_logger.debug(f"Path after prefix and stripping slashes: '{path_after_prefix}'")

        if not path_after_prefix: # Corresponds to "AnyShare://"
            if is_file_target:
                anyshare_client_logger.error(f"Cannot target the root 'AnyShare://' as a file: '{remote_path}'.")
                raise AnyshareApiException(
                    message=f"Cannot target the root 'AnyShare://' as a file: '{remote_path}'.",
                    location="AnyshareClient._extract_anyshare_path_components"
                )
            anyshare_client_logger.debug(f"Path '{remote_path}' resolves to root. Parent/name extraction returns empty strings for root context.")
            return "", "" # Representing root, parent is effectively non-existent, name is root

        api_parent_path_segment, target_name = os.path.split(path_after_prefix)
        anyshare_client_logger.debug(f"Initial os.path.split: parent='{api_parent_path_segment}', name='{target_name}'")

        if not target_name: # e.g., "AnyShare://folder1/" becomes path_after_prefix="folder1", split gives ("folder1", "")
             if not is_file_target: # If it's a directory target like "AnyShare://folder1/"
                target_name = api_parent_path_segment.split('/')[-1] # target_name becomes "folder1"
                api_parent_path_segment = os.path.dirname(api_parent_path_segment) if '/' in api_parent_path_segment else "" # parent becomes ""
                anyshare_client_logger.debug(f"Adjusted for directory-like path: parent='{api_parent_path_segment}', name='{target_name}'")

        if not target_name and is_file_target: # "AnyShare://folder1/" should not be a file target if name is empty after logic
            anyshare_client_logger.error(f"Could not extract a valid target name from '{remote_path}' (processed to: '{path_after_prefix}') for a file target.")
            raise AnyshareApiException(
                message=f"Could not extract a valid target name from '{remote_path}' (processed to: '{path_after_prefix}') for a file target.",
                location="AnyshareClient._extract_anyshare_path_components"
            )
        
        anyshare_client_logger.debug(f"Successfully extracted components for '{remote_path}': parent_segment='{api_parent_path_segment}', target_name='{target_name}'")
        return api_parent_path_segment, target_name


    # --- OSS Interaction Helpers (prints converted to logs) ---
    def _parse_auth_info_for_oss(self, auth_info: List[str]) -> Tuple[str, str, Dict[str, str]]:
        anyshare_client_logger.debug(f"Parsing auth_info for OSS operation (first item: {auth_info[0] if auth_info else 'N/A'}, item count: {len(auth_info)}).")
        if not auth_info or len(auth_info) < 2:
            anyshare_client_logger.error(f"auth_info must contain at least method and URL for OSS operation. Received: {auth_info}")
            raise AnyshareApiException(
                message="auth_info must contain at least method and URL for OSS operation.",
                location="AnyshareClient._parse_auth_info_for_oss",
                details=f"Received auth_info: {auth_info}"
            )
        http_method = auth_info[0].upper()
        oss_url = auth_info[1]
        oss_headers: Dict[str, str] = {
            k.strip(): v.strip()
            for header_str in auth_info[2:]
            if ':' in header_str
            for k, v in [header_str.split(":", 1)]
        }
        anyshare_client_logger.debug(f"Parsed OSS auth: Method='{http_method}', URL='{oss_url}', Headers={oss_headers}")
        return http_method, oss_url, oss_headers

    def _upload_to_oss(self, auth_info: List[str], local_file_path: str = None, data_chunk: bytes = None, part_number: int = None) -> Optional[str]:
        operation_context = f"(Part: {part_number})" if part_number else "(Single)"
        anyshare_client_logger.debug(f"Attempting OSS upload {operation_context}. Local file: {local_file_path}, Data chunk provided: {data_chunk is not None}.")
        
        http_method, oss_url, oss_headers = self._parse_auth_info_for_oss(auth_info)
        data_to_upload: bytes
        
        try:
            if local_file_path is not None:
                if not os.path.exists(local_file_path):
                    anyshare_client_logger.error(f"Local file not found for OSS upload {operation_context}: {local_file_path}")
                    raise AnyshareApiException(message=f"Local file not found for OSS upload: {local_file_path}", location="AnyshareClient._upload_to_oss")
                with open(local_file_path, 'rb') as f:
                    data_to_upload = f.read() 
                anyshare_client_logger.debug(f"Read {len(data_to_upload)} bytes from local file '{local_file_path}' for OSS upload {operation_context}.")
            elif data_chunk is not None:
                data_to_upload = data_chunk
                oss_headers["Content-Length"] = str(len(data_chunk))
                anyshare_client_logger.debug(f"Using provided data chunk of {len(data_chunk)} bytes for OSS upload {operation_context}. Content-Length header set.")
            else:
                anyshare_client_logger.error(f"Either local_file_path or data_chunk must be provided for OSS upload {operation_context}.")
                raise AnyshareApiException(
                    message="Either local_file_path or data_chunk must be provided for OSS upload.",
                    location="AnyshareClient._upload_to_oss"
                )

            oss_headers.setdefault("Content-Type", "application/octet-stream")
            anyshare_client_logger.debug(f"OSS Upload {operation_context}: HTTP Method='{http_method}', URL='{oss_url}', Headers='{oss_headers}'.")

            if http_method not in ["PUT", "POST"]:
                error_msg = f"Unsupported HTTP method for OSS upload: {http_method} {operation_context}"
                anyshare_client_logger.error(error_msg)
                raise AnyshareApiException(message=error_msg, location="AnyshareClient._upload_to_oss")
            
            anyshare_client_logger.info(f"Uploading to OSS {operation_context}: {http_method} {oss_url}")
            oss_response = requests.request(http_method, oss_url, headers=oss_headers, data=data_to_upload, verify=self._requests_verify_ssl)
            anyshare_client_logger.debug(f"OSS upload {operation_context} response status: {oss_response.status_code}, Headers: {oss_response.headers}")
            oss_response.raise_for_status()

            if part_number is not None: 
                etag = oss_response.headers.get("ETag") or oss_response.headers.get("etag")
                if etag:
                    etag_cleaned = etag.strip('"')
                    anyshare_client_logger.info(f"OSS upload successful for part {part_number}. ETag: '{etag_cleaned}'.")
                    return etag_cleaned
                anyshare_client_logger.error(f"ETag not found in OSS response headers for part {part_number}. URL: {oss_url}, Headers: {oss_response.headers}")
                raise AnyshareApiException(
                    message=f"ETag not found in OSS response headers for part {part_number}.",
                    location="AnyshareClient._upload_to_oss",
                    details=f"URL: {oss_url}, Headers: {oss_response.headers}"
                )
            anyshare_client_logger.info(f"OSS single-part upload successful for URL: {oss_url}.")
            return None # No ETag expected for single non-multipart uploads via this helper typically
        except requests.exceptions.HTTPError as e:
            anyshare_client_logger.error(f"OSS HTTP error during upload {operation_context}. URL: {oss_url}, Status: {e.response.status_code if e.response else 'N/A'}, Response: {e.response.text[:200]}", exc_info=True)
            raise AnyshareApiException(
                message=f"OSS HTTP error during upload {operation_context}.",
                location="AnyshareClient._upload_to_oss",
                original_exception=e,
                details=f"URL: {oss_url}, Status: {e.response.status_code if e.response else 'N/A'}, Response: {e.response.text[:200]}"
            ) from e
        except requests.exceptions.RequestException as e:
            anyshare_client_logger.error(f"OSS request error during upload {operation_context}. URL: {oss_url}", exc_info=True)
            raise AnyshareApiException(
                message=f"OSS request error during upload {operation_context}.",
                location="AnyshareClient._upload_to_oss",
                original_exception=e,
                details=f"URL: {oss_url}"
            ) from e
        except Exception as e:
            anyshare_client_logger.error(f"Unexpected error during OSS upload {operation_context}. URL: {oss_url}", exc_info=True)
            raise AnyshareApiException(
                message=f"Unexpected error during OSS upload {operation_context}.",
                location="AnyshareClient._upload_to_oss",
                original_exception=e
            ) from e

    def _download_from_oss(self, auth_info: List[str], local_save_path: str) -> None:
        current_url = ""
        try:
            http_method, oss_url, oss_headers = self._parse_auth_info_for_oss(auth_info)
            if http_method != "GET":
                anyshare_client_logger.error(f"Unsupported HTTP method for OSS download: {http_method}")
                raise AnyshareApiException(
                    message=f"Unsupported HTTP method for OSS download: {http_method}",
                    location="AnyshareClient._download_from_oss"
                )

            current_url = oss_url
            response = requests.get(current_url, headers=oss_headers, stream=True, verify=self._requests_verify_ssl, allow_redirects=False)

            if response.status_code in [301, 302, 303, 307, 308] and 'Location' in response.headers:
                redirect_count = 0
                while response.status_code in [301, 302, 303, 307, 308] and 'Location' in response.headers:
                    if redirect_count >= _MAX_BATCH_DOWNLOAD_REDIRECTS:
                        anyshare_client_logger.error(f"Too many redirects encountered during OSS download.")
                        raise AnyshareApiException(
                            message=f"Too many redirects encountered during OSS download.",
                            location="AnyshareClient._download_from_oss",
                            details=f"Last URL: {current_url}, Max redirects: {_MAX_BATCH_DOWNLOAD_REDIRECTS}"
                        )
                    redirect_location = response.headers['Location']
                    current_url = urljoin(current_url, redirect_location) if not urlparse(redirect_location).scheme else redirect_location
                    anyshare_client_logger.info(f"Redirecting OSS download to: {current_url}")
                    response = requests.get(current_url, headers=oss_headers, stream=True, verify=self._requests_verify_ssl, allow_redirects=False)
                    redirect_count += 1
            
            response.raise_for_status()
            
            os.makedirs(os.path.dirname(local_save_path), exist_ok=True)
            with open(local_save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=_DEFAULT_CHUNK_SIZE):
                    f.write(chunk)
        except requests.exceptions.HTTPError as e:
            anyshare_client_logger.error(f"OSS HTTP error during download.")
            raise AnyshareApiException(
                message="OSS HTTP error during download.",
                location="AnyshareClient._download_from_oss",
                original_exception=e
            ) from e
        except requests.exceptions.RequestException as e:
            anyshare_client_logger.error(f"OSS request error during download.")
            raise AnyshareApiException(
                message="OSS request error during download.",
                location="AnyshareClient._download_from_oss",
                original_exception=e
            ) from e
        except Exception as e:
            anyshare_client_logger.error(f"Unexpected error during OSS download.")
            raise AnyshareApiException(
                message="Unexpected error during OSS download.",
                location="AnyshareClient._download_from_oss",
                original_exception=e
            ) from e

    # --- Public API Methods (Refactored examples) ---
    
    def path_to_gns(self, remote_path: str) -> str:
        anyshare_client_logger.info(f"Converting AnyShare path '{remote_path}' to GNS.")
        if not remote_path.startswith("AnyShare://"):
            anyshare_client_logger.warning(f"Invalid remote_path format for path_to_gns: '{remote_path}'. Must start with 'AnyShare://'. Returning empty GNS.")
            return ""
            
        item_info = self._get_item_info_by_path_lenient(remote_path)
        
        if item_info and isinstance(item_info, FileGetinfobypathRes) and item_info.docid:
            anyshare_client_logger.info(f"Path '{remote_path}' resolved to GNS '{item_info.docid}'.")
            return item_info.docid
        
        if item_info and not item_info.docid:
            anyshare_client_logger.error(f"Retrieved item info for path '{remote_path}', but GNS (docid) is missing in the response.")
        
        return "" 

    def gns_to_path(self, gns: str) -> str:
        operation_desc = f"convert GNS '{gns}' to path"
        
        req_data = FileConvertpathReq(docid=gns)
        try:
            result = self._call_api(
                api_name="file_operation_api",
                method_name="convert_path",
                req_data=req_data,
                success_model_type=FileConvertpathRes,
                operation_description=operation_desc
            )

            if result and isinstance(result, FileConvertpathRes):
                if result.namepath is not None:
                    return f"AnyShare://{result.namepath}"
                anyshare_client_logger.warning(f"Received null or unexpected namepath for GNS '{gns}' in {operation_desc}.")
        except AnyshareApiException as e:
            # If the API call fails (e.g., due to invalid GNS format leading to 400 error),
            # log a warning and return an empty string, as expected by tests for invalid input.
            anyshare_client_logger.warning(f"{operation_desc} failed due to API error: {e}. Returning empty path.")
        except Exception as e:
            # Catch any other unexpected errors during the process
            anyshare_client_logger.error(f"Unexpected error during {operation_desc}: {e}. Returning empty path.")

        return ""

    def config_client(self, host="http://files.flexiv.cloud", port=88):
        self._config.host = host
        self._config.port = port
        anyshare_client_logger.info(f"Client configured to host: {host}, port: {port}")

    def login(self, username: str, password: str) -> bool:
        self._config.username = username
        
        encrypted_password = self._encrypt_text(password)
        if not encrypted_password:
            anyshare_client_logger.error(f"Login failed for user '{username}': Password encryption step failed.")
            return False

        auth_getnew_req = Auth1GetnewReq(account=username, password=encrypted_password)
        operation_desc = f"authenticate user '{username}'"
        
        login_res = self._call_api(
            api_name="auth_api",
            method_name="auth_get_new",
            req_data=auth_getnew_req,
            success_model_type=Auth1GetnewRes,
            operation_description=operation_desc
        )

        if login_res and isinstance(login_res, Auth1GetnewRes):
            if hasattr(login_res, 'tokenid') and login_res.tokenid:
                self._config.token_id = login_res.tokenid
                self._config.user_id = login_res.userid
                self._config.token_id_expires = login_res.expires
                anyshare_client_logger.info(f"Login success for user '{username}'.")
                anyshare_client_logger.debug(f"User ID: {self._config.user_id}, Token expires: {self._config.token_id_expires}")
                anyshare_client_logger.debug(f"Login token: {self._config.token_id}")
                return True
            else:
                anyshare_client_logger.error(f"Login failed for user '{username}': 'tokenid' missing or empty in API response. Response: {login_res}")
                return False
        else:
            anyshare_client_logger.error(f"Login attempt for user '{username}' failed. See previous API error logs if any.")
            return False
        
    def logout(self) -> bool:
        if not self._config.token_id:
            anyshare_client_logger.info("Logout skipped: No active session (token_id is not set).")
            return False

        auth_logout_req = Auth1LogoutReq(ostype=0)
        operation_desc = f"logout user '{self._config.username or self._config.user_id}'"

        api_call_result = self._call_api(
            api_name="auth_api",
            method_name="auth_logout",
            req_data=auth_logout_req,
            success_model_type=type(None),
            operation_description=operation_desc
        )

        if api_call_result is AnyshareClient._SUCCESS_NO_CONTENT:
            anyshare_client_logger.info(f"Logout success for user '{self._config.username or self._config.user_id}'.")
            self._config.token_id = ""
            self._config.user_id = ""
            self._config.token_id_expires = ""
            self._config.username = ""
            return True
        else:
            return False

    def _get_remote_parent_gns_and_filename(self, remote_target_file_path: str) -> Tuple[str, str]:
        api_parent_path_segment, filename = self._extract_anyshare_path_components(remote_target_file_path, is_file_target=True)

        if api_parent_path_segment is None or filename is None:
            return None, None 

        parent_remote_path_for_gns_lookup: str
        if not api_parent_path_segment:
            parent_remote_path_for_gns_lookup = "AnyShare://"
        else:
            parent_remote_path_for_gns_lookup = f"AnyShare://{api_parent_path_segment}"
        
        parent_gns = self.path_to_gns(parent_remote_path_for_gns_lookup) 
        if not parent_gns:
             anyshare_client_logger.error(f"Could not resolve GNS for parent path: '{parent_remote_path_for_gns_lookup}' derived from '{remote_target_file_path}'")
             return None, filename

        return parent_gns, filename

    def _get_item_info_by_path_lenient(self, remote_path: str) -> Optional[FileGetinfobypathRes]:
        api_path_segment = self._get_path_segment_for_api(remote_path)
        req_data = FileGetinfobypathReq(namepath=api_path_segment)
        
        try:
            return self._call_api(
                api_name="file_operation_api",
                method_name="get_info_by_path",
                req_data=req_data,
                success_model_type=FileGetinfobypathRes,
                operation_description=f"get item info for '{remote_path}' (lenient)"
            )
        except AnyshareApiException as e:
            current_exception: Optional[BaseException] = e
            is_acceptable_not_found = False

            while isinstance(current_exception, AnyshareApiException):
                if current_exception.error_code == 404006:
                    is_acceptable_not_found = True
                    break
                
                if current_exception.error_code == 404 and "not found" in str(e).lower():
                    if "Failed to parse JSON error response into Error model" in str(current_exception) and \
                       isinstance(current_exception.original_exception, Exception) and \
                       current_exception.original_exception is not None and \
                       "errcode=404006" in str(current_exception.original_exception).lower(): # Check original Pydantic error details
                        is_acceptable_not_found = True
                        break
                    
                    if current_exception.error_code == 404 and \
                       "Failed to parse JSON error response into Error model" in str(current_exception) and \
                       "file/getinfobypath" in str(current_exception): # Specific to this API call
                        anyshare_client_logger.debug(f"Leniently treating 404 with Error model parsing failure for '{remote_path}' as 'not found'. Original exception: {current_exception.original_exception}")
                        is_acceptable_not_found = True
                        break
                        
                if not hasattr(current_exception, 'original_exception') or \
                   not isinstance(current_exception.original_exception, AnyshareApiException) or \
                   current_exception.original_exception is None: # Stop if no further chained AnyshareApiException
                    break
                current_exception = current_exception.original_exception
            
            if is_acceptable_not_found:
                anyshare_client_logger.debug(f"Path '{remote_path}' not found (API Error Code 404006 or equivalent 404), returning None as per lenient policy. Original exception details: {e}")
                return None
            else:
                raise
        except Exception:
            raise

    # --- File Operations (Example refactor for upload_file_single) ---
    def upload_file_single(self, local_file_path: str, remote_target_file_path: str, ondup: int = 2) -> Optional[Tuple[str, str, str]]:
        operation_desc = f"single-part upload of '{os.path.basename(local_file_path)}' to '{remote_target_file_path}'"
        anyshare_client_logger.info(f"Starting {operation_desc}. Ondup policy: {ondup}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        if not os.path.exists(local_file_path):
            anyshare_client_logger.error(f"{operation_desc} failed: Local file not found: {local_file_path}")
            return None

        file_length_bytes = os.path.getsize(local_file_path)
        if file_length_bytes >= _SINGLE_UPLOAD_MAX_BYTES:
            anyshare_client_logger.error(
                f"{operation_desc} failed: File size ({file_length_bytes / (1024*1024*1024):.2f} GiB) "
                f"exceeds single-part limit ({_SINGLE_UPLOAD_MAX_BYTES / (1024*1024*1024):.2f} GiB). Use multipart upload instead."
            )
            return None
        
        client_mtime_ms = int(os.path.getmtime(local_file_path) * 1000)
        parent_gns, filename = self._get_remote_parent_gns_and_filename(remote_target_file_path)

        if not parent_gns or not filename:
            anyshare_client_logger.error(f"{operation_desc} failed: Could not determine parent GNS or filename.")
            return None

        begin_req_data = FileOsbeginuploadReq(
            docid=parent_gns, length=file_length_bytes, name=filename,
            client_mtime=client_mtime_ms, ondup=ondup
        )
        
        begin_response = self._call_api("file_transfer_api", "os_begin_upload", begin_req_data, FileOsbeginuploadRes, f"begin single-part upload for {filename}")
        if not begin_response: return None

        try:
            self._upload_to_oss(begin_response.authrequest, local_file_path=local_file_path)
        except Exception as e:
            anyshare_client_logger.error(f"OSS upload step failed for {operation_desc}: {e}")
            return None

        end_req_data = FileOsenduploadReq(docid=begin_response.docid, rev=begin_response.rev)
        end_response = self._call_api("file_transfer_api", "os_end_upload", end_req_data, FileOsenduploadRes, f"end single-part upload for {filename}")
        if not end_response: return None
        
        anyshare_client_logger.info(f"Successfully uploaded '{filename}' to '{remote_target_file_path}'. GNS: {begin_response.docid}, Rev: {begin_response.rev}.")
        return begin_response.docid, begin_response.rev, begin_response.name


    def create_directory(self, remote_directory_path: str) -> bool: 
        operation_desc = f"create directory '{remote_directory_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)

        if not remote_directory_path.startswith("AnyShare://") or remote_directory_path == "AnyShare://":
            anyshare_client_logger.error(f"{operation_desc} failed: Invalid path. Must start with 'AnyShare://' and not be the root itself.")
            return False

        path_components = [part for part in self._get_path_segment_for_api(remote_directory_path).strip('/').split('/') if part]
        if not path_components:
            anyshare_client_logger.error(f"{operation_desc} failed: Path resolves to empty components after parsing.")
            return False

        existing_full_path_info = self._get_item_info_by_path_lenient(remote_directory_path)
        if existing_full_path_info:
            if existing_full_path_info.size == -1: 
                anyshare_client_logger.info(f"Directory '{remote_directory_path}' already exists (GNS: {existing_full_path_info.docid}). Operation considered successful.")
                return True 
            else: 
                anyshare_client_logger.error(f"{operation_desc} failed: Path exists but is not a directory.")
                return False

        parent_gns_for_api = ""
        relative_path_to_create_for_api = ""
        path_of_found_parent_for_logging = ""

        for num_parent_comps in range(len(path_components) - 1, -1, -1):
            current_parent_sub_path_parts = path_components[:num_parent_comps]
            path_to_check_as_parent_str = "AnyShare://" + "/".join(current_parent_sub_path_parts) if current_parent_sub_path_parts else "AnyShare://"
            
            parent_info = self._get_item_info_by_path_lenient(path_to_check_as_parent_str)
            if parent_info and parent_info.docid and parent_info.size == -1:
                parent_gns_for_api = parent_info.docid
                relative_path_to_create_for_api = "/".join(path_components[num_parent_comps:])
                path_of_found_parent_for_logging = path_to_check_as_parent_str
                anyshare_client_logger.info(f"Found existing parent '{path_of_found_parent_for_logging}' (GNS: {parent_gns_for_api}) for {operation_desc}. Relative path to create: '{relative_path_to_create_for_api}'")
                break
        
        if not parent_gns_for_api: 
            anyshare_client_logger.debug(f"No intermediate parent directory found for '{remote_directory_path}'. Assuming root as the base parent.")
            root_info = self._get_item_info_by_path_lenient("AnyShare://")
            if root_info and root_info.docid and root_info.size == -1:
                parent_gns_for_api = root_info.docid
                relative_path_to_create_for_api = "/".join(path_components) 
                path_of_found_parent_for_logging = "AnyShare://"
                if not relative_path_to_create_for_api: 
                     anyshare_client_logger.error(f"{operation_desc} failed: Fallback to root resulted in empty relative path.")
                     return False
                anyshare_client_logger.info(f"Using root directory 'AnyShare://' (GNS '{parent_gns_for_api}') to create full relative path '{relative_path_to_create_for_api}' for {operation_desc}.")
            else:
                err_msg_detail = f"Root 'AnyShare://' could not be resolved or is not a directory."
                if root_info : err_msg_detail = f"Root 'AnyShare://' (GNS: {root_info.docid}) is not a directory (size: {root_info.size})."
                anyshare_client_logger.error(f"{operation_desc} failed: Could not determine a valid existing base parent. {err_msg_detail}")
                return False
        
        if not relative_path_to_create_for_api: 
            anyshare_client_logger.error(f"{operation_desc} failed: Calculated relative path to create is empty. This should not occur.")
            return False

        req = DirCreatemultileveldirReq(docid=parent_gns_for_api, path=relative_path_to_create_for_api)
        api_op_desc = f"API call to create multilevel directory '{relative_path_to_create_for_api}' under GNS '{parent_gns_for_api}'"
        result = self._call_api("directory_api", "create_multilevel_directory", req, DirCreatemultileveldirRes, api_op_desc)
        
        if result and isinstance(result, DirCreatemultileveldirRes):
            anyshare_client_logger.info(f"Directory path '{remote_directory_path}' created/ensured. Last component GNS: {result.docid}.")
            return True
        else:
            # Error logged by _call_api
            return False

    # --- Other methods (stubs for brevity, need full refactoring as above) ---
    
    def _upload_file_parts(self, local_file_path: str, file_length_bytes: int, target_docid: str, 
                           target_rev: str, upload_id: str, part_size_bytes: int) -> Optional[Dict[str, List[Any]]]:
        parts_info: Dict[str, List[Any]] = {}
        if file_length_bytes == 0: return parts_info 
        num_parts = math.ceil(file_length_bytes / part_size_bytes) if part_size_bytes > 0 else 0
        if num_parts == 0 and file_length_bytes > 0 : raise ValueError("Calculated zero parts for non-empty file.")
        with open(local_file_path, 'rb') as f:
            for part_number_one_based in range(1, num_parts + 1):
                data_chunk = f.read(part_size_bytes)
                if not data_chunk: raise EOFError("Unexpected EOF.")
                part_auth_req = FileOsuploadpartReq(docid=target_docid, rev=target_rev, uploadid=upload_id, parts=str(part_number_one_based))
                part_auth_res = self._call_api("file_transfer_api", "os_upload_part", part_auth_req, FileOsuploadpartRes, f"upload part {part_number_one_based}")
                if not part_auth_res: return None
                auth_info_for_part = part_auth_res.authrequests.get(str(part_number_one_based))
                if not auth_info_for_part: raise ValueError("Auth info not found for part.")
                etag = self._upload_to_oss(auth_info_for_part, data_chunk=data_chunk, part_number=part_number_one_based)
                if etag is None: raise ValueError("Missing ETag.")
                parts_info[str(part_number_one_based)] = [etag, len(data_chunk)]
        if file_length_bytes > 0 and len(parts_info) != num_parts: raise ValueError("Mismatch in uploaded parts.")
        return parts_info
        
    def upload_file_multipart(self, local_file_path: str, remote_target_file_path: str, ondup: int = 2, 
                              part_size_bytes: int = _DEFAULT_MULTIPART_PART_SIZE) -> Optional[Tuple[str, str, str]]:
        operation_desc = f"multipart upload of '{os.path.basename(local_file_path)}' to '{remote_target_file_path}'"
        anyshare_client_logger.info(f"Starting {operation_desc}. Ondup: {ondup}, Part size: {part_size_bytes} bytes.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        if not os.path.exists(local_file_path): anyshare_client_logger.error(f"{operation_desc} failed: Local file not found."); return None

        file_length_bytes = os.path.getsize(local_file_path)
        client_mtime_ms = int(os.path.getmtime(local_file_path) * 1000)
        parent_gns, filename = self._get_remote_parent_gns_and_filename(remote_target_file_path)
        if not parent_gns or not filename: anyshare_client_logger.error(f"{operation_desc} failed: Could not get parent GNS/filename."); return None
        
        if file_length_bytes > 0 and part_size_bytes < _MIN_MULTIPART_PART_SIZE_S3_LIKE:
             anyshare_client_logger.warning(f"Part size ({part_size_bytes}) < min ({_MIN_MULTIPART_PART_SIZE_S3_LIKE} bytes). Adjusting.")
             part_size_bytes = _MIN_MULTIPART_PART_SIZE_S3_LIKE
        elif part_size_bytes <=0 and file_length_bytes > 0:
            anyshare_client_logger.error(f"{operation_desc} failed: part_size_bytes must be positive.")
            return None

        init_req_data = FileOsinitmultiuploadReq(docid=parent_gns, length=file_length_bytes, name=filename, client_mtime=client_mtime_ms, ondup=ondup)
        init_response = self._call_api("file_transfer_api", "os_init_multiupload", init_req_data, FileOsinitmultiuploadRes, "init multipart upload")
        if not init_response: return None
        
        parts_info = {}
        if file_length_bytes > 0 :
            if part_size_bytes <=0: anyshare_client_logger.error(f"{operation_desc} failed: part_size_bytes positive error again."); return None
            parts_info_res = self._upload_file_parts(local_file_path, file_length_bytes, init_response.docid, init_response.rev, init_response.uploadid, part_size_bytes)
            
            if parts_info_res is None: anyshare_client_logger.error(f"{operation_desc} failed: _upload_file_parts returned None."); return None
            parts_info = parts_info_res
        
        complete_req_data = FileOscompleteuploadReq(docid=init_response.docid, rev=init_response.rev, uploadid=init_response.uploadid, partinfo=parts_info)
        complete_response = self._call_api("file_transfer_api", "os_complete_upload", complete_req_data, FileOscompleteuploadRes, "complete multipart upload")
        if not complete_response: return None
        
        anyshare_client_logger.info(f"Multipart upload of '{filename}' to '{remote_target_file_path}' complete. GNS: {init_response.docid}, Rev: {init_response.rev}.")
        return init_response.docid, init_response.rev, init_response.name
        
    def upload_file(self, local_file_path: str, remote_target_file_path: str, ondup: int = 2, 
                    part_size_bytes_for_multipart: int = _DEFAULT_MULTIPART_PART_SIZE) -> Optional[Tuple[str, str, str]]:
        operation_desc = f"upload file '{os.path.basename(local_file_path)}' to '{remote_target_file_path}'"
        anyshare_client_logger.info(f"Processing {operation_desc}. Ondup: {ondup}, Multipart part size (if used): {part_size_bytes_for_multipart} bytes.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        if not os.path.exists(local_file_path): anyshare_client_logger.error(f"Upload failed: Local file not found: {local_file_path}"); return None
        file_length_bytes = os.path.getsize(local_file_path)
        file_basename = os.path.basename(local_file_path)
        if file_length_bytes < _SINGLE_UPLOAD_MAX_BYTES:
            anyshare_client_logger.info(f"File size {file_length_bytes / (1024*1024):.2f} MB. Using single part upload for '{file_basename}'.")
            return self.upload_file_single(local_file_path, remote_target_file_path, ondup)
        else:
            anyshare_client_logger.info(f"File size {file_length_bytes / (1024*1024):.2f} MB. Using multipart upload for '{file_basename}'.")
            return self.upload_file_multipart(local_file_path, remote_target_file_path, ondup, part_size_bytes=part_size_bytes_for_multipart)

    def download_file(self, remote_file_path: str, local_download_directory: str, 
                      file_rev: Optional[str] = None, local_filename_override: Optional[str] = None) -> Optional[str]:
        operation_desc = f"download file '{remote_file_path}'"
        anyshare_client_logger.info(f"Starting {operation_desc} to directory '{local_download_directory}'. Revision: {file_rev or 'latest'}, Local filename override: {local_filename_override or 'None'}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        file_gns = self.path_to_gns(remote_file_path)
        if not file_gns: anyshare_client_logger.error(f"{operation_desc} failed: Could not resolve GNS."); return None

        auth_req_data = FileOsdownloadReq(docid=file_gns, rev=file_rev if file_rev else None)
        auth_response = self._call_api("file_transfer_api", "os_download", auth_req_data, FileOsdownloadRes, f"get download auth for {remote_file_path}")
        if not auth_response: return None
        
        server_filename = auth_response.name
        local_filename_to_use = os.path.basename(local_filename_override if local_filename_override else server_filename)
        if not local_filename_to_use: anyshare_client_logger.error(f"{operation_desc} failed: Filename for saving could not be determined."); return None

        abs_download_dir = os.path.abspath(local_download_directory)
        local_save_path = os.path.join(abs_download_dir, local_filename_to_use)
        try:
            self._download_from_oss(auth_response.authrequest, local_save_path)
            anyshare_client_logger.info(f"File '{remote_file_path}' (rev: {auth_response.rev}, name: {auth_response.name}) downloaded to '{local_save_path}'")
            return local_save_path
        except Exception as e:
            anyshare_client_logger.error(f"OSS download step failed for {operation_desc}: {e}")
            return None
        
    def download_batch(self, remote_paths: List[str], local_download_directory: str, 
                       zip_name: str, reqhost: str) -> Optional[str]:
        operation_desc = f"batch download to '{zip_name}'"
        anyshare_client_logger.info(f"Starting {operation_desc} to directory '{local_download_directory}'. Reqhost: '{reqhost}'.")
        
        if not remote_paths: anyshare_client_logger.error(f"{operation_desc} failed: No remote paths provided."); return None

        files_gns: List[str] = []
        dirs_gns: List[str] = []

        for path_str in remote_paths:
            item_info = self._get_item_info_by_path_lenient(path_str)
            if item_info and item_info.docid:
                if item_info.size == -1: 
                    dirs_gns.append(item_info.docid)
                    anyshare_client_logger.info(f"Added directory '{path_str}' (GNS: {item_info.docid}) to batch download.")
                else: 
                    files_gns.append(item_info.docid)
                    anyshare_client_logger.info(f"Added file '{path_str}' (GNS: {item_info.docid}) to batch download.")
            else:
                 anyshare_client_logger.warning(f"Skipping path '{path_str}' from batch download due to missing info or docid (see previous debug logs).")

        if not files_gns and not dirs_gns:
            anyshare_client_logger.error(f"{operation_desc} failed: No valid files or directories found from provided paths.")
            return None

        request_zip_name = zip_name if zip_name.lower().endswith(".zip") else f"{zip_name}.zip"
        batch_req_data = FileBatchdownloadReq(name=request_zip_name, reqhost=reqhost, usehttps=False, files=files_gns or None, dirs=dirs_gns or None)
        
        batch_response = self._call_api("file_transfer_api", "batch_download", batch_req_data, FileBatchdownloadRes, "initiate batch download")
        if not batch_response: return None
        
        if batch_response.method.upper() != "GET":
            anyshare_client_logger.error(f"{operation_desc} failed: Unsupported method '{batch_response.method}' for batch download URL.")
            return None

        abs_download_dir = os.path.abspath(local_download_directory)
        request_zip_name_base = os.path.basename(request_zip_name)
        local_save_path = os.path.join(abs_download_dir, request_zip_name_base)
        os.makedirs(os.path.dirname(local_save_path), exist_ok=True)
        current_url = batch_response.url

        for attempt in range(_MAX_BATCH_DOWNLOAD_REDIRECTS + 1): 
            response = None 
            try:
                anyshare_client_logger.info(f"Attempting batch download from: {current_url} (attempt {attempt + 1}/{_MAX_BATCH_DOWNLOAD_REDIRECTS + 1})")
                response = requests.get(current_url, stream=True, verify=self._requests_verify_ssl, allow_redirects=False, timeout=60)
                if 200 <= response.status_code < 300: 
                    with open(local_save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=_DEFAULT_CHUNK_SIZE): f.write(chunk)
                    anyshare_client_logger.info(f"Batch download '{request_zip_name_base}' saved to '{local_save_path}'.")
                    return local_save_path
                if 300 <= response.status_code < 400 and 'Location' in response.headers: 
                    if attempt >= _MAX_BATCH_DOWNLOAD_REDIRECTS:
                        anyshare_client_logger.error(f"{operation_desc} failed: Too many redirects. Last URL: {current_url}")
                        return None
                    redirect_location = response.headers['Location']
                    current_url = urljoin(current_url, redirect_location) if not urlparse(redirect_location).scheme else redirect_location
                    anyshare_client_logger.info(f"Batch download redirecting to: {current_url}")
                else: 
                    response.raise_for_status()
                    anyshare_client_logger.error(f"{operation_desc} failed with status {response.status_code} at {current_url} after non-redirect error.")
                    return None
            except requests.exceptions.RequestException as e:
                anyshare_client_logger.error(f"Batch download attempt {attempt + 1} failed: {e}")
                if attempt >= _MAX_BATCH_DOWNLOAD_REDIRECTS: 
                    anyshare_client_logger.error(f"{operation_desc} failed after {attempt +1} attempts. Last URL: {current_url}. Error: {e}")
                    return None
        anyshare_client_logger.error(f"{operation_desc} failed after all attempts. Last URL was: {current_url}")
        return None

    def rename_file(self, remote_file_path: str, new_name: str, ondup: int = 1) -> bool:
        operation_desc = f"rename file '{remote_file_path}' to '{new_name}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_file_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed: Cannot get GNS for '{remote_file_path}'."); return False
        
        if os.path.dirname(new_name): anyshare_client_logger.error(f"{operation_desc} failed: new_name must be filename only."); return False

        rename_req = FileRenameReq(docid=docid, name=new_name, ondup=ondup)
        result = self._call_api("file_operation_api", "rename_file", rename_req, FileRenameRes, operation_desc)
        
        if result and isinstance(result, FileRenameRes):
            final_name = result.name if result.name else new_name
            anyshare_client_logger.info(f"Renamed '{remote_file_path}' to '{final_name}'.")
            return True
        return False

    def delete_file(self, remote_file_path: str) -> bool:
        operation_desc = f"delete file '{remote_file_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_file_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed: Cannot get GNS for '{remote_file_path}'."); return False
        
        delete_req = FileDeleteReq(docid=docid)
        api_call_result = self._call_api("file_operation_api", "delete_file", delete_req, type(None), operation_desc)
        
        if api_call_result is AnyshareClient._SUCCESS_NO_CONTENT:
            anyshare_client_logger.info(f"File '{remote_file_path}' deleted.")
            return True
        return False
    
    def get_file_custom_attributes(self, remote_file_path: str) -> Optional[List[FileGetfilecustomattributeRes]]:
        operation_desc = f"get custom attributes for '{remote_file_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_file_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed to get GNS for '{remote_file_path}'."); return None
        
        req = FileGetfilecustomattributeReq(docid=docid)
        
        response_union = self._api["file_operation_api"].get_file_custom_attribute(req)
        try:
            if isinstance(response_union, list) and all(isinstance(item, FileGetfilecustomattributeRes) for item in response_union):
                 anyshare_client_logger.info(f"Retrieved {len(response_union)} custom attributes for '{remote_file_path}'.")
                 return response_union
            elif isinstance(response_union, Error):
                raise AnyshareApiException(message=f"API error getting custom attributes: {response_union.errmsg}", error_code=response_union.errcode, location=operation_desc)
            else:
                raise AnyshareApiException(message=f"Unexpected response type getting custom attributes: {type(response_union)}", location=operation_desc)

        except AnyshareApiException as e:
            anyshare_client_logger.error(f"{operation_desc} failed: {e}")
            return None
        except Exception as e:
            raise AnyshareApiException(message=f"{operation_desc} encountered an unexpected error.", original_exception=e, location="AnyshareClient.get_file_custom_attributes") from e


    def set_file_custom_attributes(self, remote_file_path: str, attributes: List[Dict[str, Any]]) -> bool:
        operation_desc = f"set custom attributes for '{remote_file_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_file_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed to get GNS for '{remote_file_path}'."); return False
        
        attr_models: List[FileSetfilecustomattributeReqAttribute] = []
        for attr_dict in attributes:
            attr_id_any = attr_dict.get('id')
            attr_value = attr_dict.get('value')
            
            if attr_id_any is not None:
                try:
                    attr_id_int = int(attr_id_any) 
                    attr_models.append(FileSetfilecustomattributeReqAttribute(id=attr_id_int, value=attr_value))
                    anyshare_client_logger.debug(f"Successfully prepared attribute model for GNS '{docid}': id={attr_id_int}, value='{attr_value}'.")
                except ValueError: 
                    anyshare_client_logger.warning(f"Attribute ID '{attr_id_any}' for GNS '{docid}' is not a valid integer. Skipping this attribute in {operation_desc}.")
                except Exception as e: # Catch other potential errors during model creation (though less likely for this simple model)
                    anyshare_client_logger.warning(f"Could not convert attribute {attr_dict} to model for GNS '{docid}' due to: {e}. Skipping this attribute in {operation_desc}.", exc_info=True)
            else: # Missing 'id'
                anyshare_client_logger.warning(f"Skipping attribute due to missing 'id' in {operation_desc} for GNS '{docid}': {attr_dict}")

        if not attr_models and attributes: # Original attributes list was not empty, but no valid models were created
            anyshare_client_logger.error(f"{operation_desc} failed for GNS '{docid}': No valid attributes to set after processing input list. Input was: {attributes}")
            return False
        if not attr_models: # No attributes were provided or all were invalid. If original list was empty, this is fine.
            anyshare_client_logger.info(f"No valid attributes to set for '{remote_file_path}' (GNS '{docid}'). Operation completed (no API call made if list is empty).")
            return True

        req = FileSetfilecustomattributeReq(docid=docid, attribute=attr_models)
        api_call_result = self._call_api("file_operation_api", "set_file_custom_attribute", req, type(None), operation_desc)
        
        if api_call_result is AnyshareClient._SUCCESS_NO_CONTENT:
            anyshare_client_logger.info(f"Custom attributes for '{remote_file_path}' set.")
            return True
        return False

    def add_file_tags(self, remote_path: str, tag_names: List[str]) -> bool:
        operation_desc = f"add tags to file '{remote_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed to get GNS for '{remote_path}'."); return False
        
        if not tag_names: anyshare_client_logger.info(f"No tag names provided to add for {operation_desc}. Operation considered successful.")
        if not tag_names: return True

        req = FileAddtagsReq(docid=docid, tags=tag_names)
        result = self._call_api("file_operation_api", "add_file_tags", req, FileAddtagsRes, operation_desc)
        
        if result and isinstance(result, FileAddtagsRes):
            anyshare_client_logger.info(f"Add tags request for '{remote_path}' processed. Max tags: {result.tag_max_num}.")
            if result.unsettagnum > 0:
                anyshare_client_logger.warning(f"{result.unsettagnum} tags could not be set for '{remote_path}': {result.unsettags}")
            return result.unsettagnum == 0
        return False

    def remove_file_tag(self, remote_path: str, tag_name: str) -> bool:
        operation_desc = f"remove tag '{tag_name}' from file '{remote_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed to get GNS for '{remote_path}'."); return False
        
        if not tag_name: anyshare_client_logger.warning(f"No tag name provided to remove for {operation_desc}."); return False
        
        req = FileDeletetagReq(docid=docid, tag=tag_name)
        api_call_result = self._call_api("file_operation_api", "delete_file_tag", req, type(None), operation_desc)
        
        if api_call_result is AnyshareClient._SUCCESS_NO_CONTENT:
            anyshare_client_logger.info(f"Tag '{tag_name}' removed from '{remote_path}'.")
            return True
        return False

    def lock_file(self, remote_file_path: str, minutes: int = 30, force: bool = False) -> bool: 
        operation_desc = f"lock file '{remote_file_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_file_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed to get GNS for '{remote_file_path}'."); return False
        
        expire_time_us = -1 if minutes <= 0 else int((time.time() + minutes * 60) * 1_000_000)
        req = AutolockLockReq(docid=docid, force=force, expiretime=expire_time_us)
        api_call_result = self._call_api("autolock_api", "lock_file", req, type(None), operation_desc)
        
        if api_call_result is AnyshareClient._SUCCESS_NO_CONTENT:
            anyshare_client_logger.info(f"File '{remote_file_path}' locked (expiretime_us: {expire_time_us}).")
            return True
        return False

    def unlock_file(self, remote_file_path: str) -> bool:
        operation_desc = f"unlock file '{remote_file_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_file_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed to get GNS for '{remote_file_path}'."); return False
        
        req = AutolockUnlockReq(docid=docid)
        api_call_result = self._call_api("autolock_api", "unlock_file", req, type(None), operation_desc)
        
        if api_call_result is AnyshareClient._SUCCESS_NO_CONTENT:
            anyshare_client_logger.info(f"File '{remote_file_path}' unlocked.")
            return True
        return False

    def delete_directory(self, remote_directory_path: str) -> bool:
        operation_desc = f"delete directory '{remote_directory_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_directory_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed: Cannot get GNS for '{remote_directory_path}'."); return False
        
        req = DirDeleteReq(docid=docid)
        result = self._call_api("directory_api", "delete_directory", req, DirDeleteRes, operation_desc)
        
        if result:
            anyshare_client_logger.info(f"Directory '{remote_directory_path}' (GNS: {docid}) delete request processed.")
            return True
        return False

    def rename_directory(self, remote_directory_path: str, new_name: str, ondup: Optional[int] = 1) -> bool:
        operation_desc = f"rename directory '{remote_directory_path}' to '{new_name}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_directory_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed: Cannot get GNS for '{remote_directory_path}'."); return False
        
        if os.path.dirname(new_name): anyshare_client_logger.error("New name should be dir name only."); return False
        
        req = DirRenameReq(docid=docid, name=new_name, ondup=ondup if ondup is not None else 1)
        result = self._call_api("directory_api", "rename_directory", req, DirRenameRes, operation_desc)
        
        if result:
            anyshare_client_logger.info(f"Directory '{remote_directory_path}' renamed to '{result.name or new_name}'.")
            return True
        return False

    def list_directory_contents(self, remote_directory_path: str, by: Optional[str] = None, sort: Optional[str] = None, attr: Optional[bool] = None) -> Optional[DirListRes]:
        operation_desc = f"list contents of directory '{remote_directory_path}'"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)
        
        docid = self.path_to_gns(remote_directory_path)
        if not docid: anyshare_client_logger.error(f"{operation_desc} failed: Cannot get GNS for '{remote_directory_path}'."); return None
        
        req = DirListReq(docid=docid, by=by, sort=sort, attr=attr)
        result = self._call_api("directory_api", "list_directory_contents", req, DirListRes, operation_desc)
        
        if result:
            anyshare_client_logger.info(f"Listed contents for '{remote_directory_path}'. Found {len(result.dirs)} dirs, {len(result.files)} files.")
        return result

    def get_accessible_doc_libs(self, doctype: int = 0) -> Optional[List[Entrydoc2GetResDocinfo]]:
        operation_desc = f"get accessible document libraries (doctype: {doctype})"
        anyshare_client_logger.info(f"Attempting to {operation_desc}.")
        
        anyshare_client_logger.debug(f"Ensuring user is logged in for {operation_desc}.")
        self._ensure_logged_in(operation_desc)

        req_data = Entrydoc2GetReq(doctype=doctype)
        result = self._call_api("entrydoc_api", "get_accessible_doc_libs", req_data, Entrydoc2GetRes, operation_desc)
        
        if result and isinstance(result, Entrydoc2GetRes):
            anyshare_client_logger.info(f"Retrieved {len(result.docinfos)} accessible document libraries for doctype {doctype}.")
            return result.docinfos
        return None
        
