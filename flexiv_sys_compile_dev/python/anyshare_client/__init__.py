#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AnyShare API Client

A Python client library for interacting with AnyShare cloud storage platform.
Provides high-level APIs for file operations, directory management, authentication,
and more.

Example:
    Basic usage:

    >>> from anyshare_client import AnyshareClient, Config
    >>> client = AnyshareClient()
    >>> client.config_client(host="your-anyshare-host.com", port=88)
    >>> success = client.login("username", "password")
    >>> if success:
    ...     client.upload_file("local_file.txt", "AnyShare://User/remote_file.txt")
"""

# Core client and configuration
from .anyshare_client import AnyshareClient

# Import the api and models modules for advanced usage
from . import api
from . import config

__version__ = "0.1.0"

# Public API
__all__ = [
    # Core classes
    "AnyshareClient",

    # Modules for advanced usage
    "api",
    "config",

    # Metadata
    "__version__"
]
