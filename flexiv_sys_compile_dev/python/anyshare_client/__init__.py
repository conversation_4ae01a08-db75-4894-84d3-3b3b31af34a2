#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""Anyshare API Client"""

from .config import Config
from .api import AuthApi, EntryDocApi, DirectoryApi, AutolockApi, FileTransferApi, FileOperationApi
from .api import models  # 导入 models 子模块
from .anyshare_client import AnyshareClient  # 修正导入
__version__ = "0.1.0"

# Potentially import other APIs or models here as they are developed

__all__ = [
    "Config",
    "AuthApi",
    "EntryDocApi",
    "DirectoryApi",
    "AutolockApi",
    "FileTransferApi",
    "FileOperationApi",
    "AnyshareClient",
    "models",  # 将 models 添加到 __all__
    "__version__"
]
