import json
import requests
import dataclasses
from anyshare_client.config import Config
from .models import *
from pydantic import BaseModel # Add pydantic BaseModel for type checking
from typing import Optional, Union
from .models.common_models import ANYSHARE_ERROR_CODES_DETAILS, AnyshareApiException # Import the error code details AND custom exception

class BaseApi:
    def __init__(self, config: Config):
        self.config = config
    def auth_request(
        self,
        relative_path: str,
        method: str,
        params: dict = None,
        data: bytes = None,
        headers: dict = None,
        cookies: dict = None,
        files: dict = None,
        auth: tuple = None,
        timeout: float = None,
        allow_redirects: bool = True,
        proxies: dict = None,
        hooks: dict = None,
        stream: bool = None,
        verify: bool = None,
        cert: tuple = None,
        json: dict = None,
        add_auth_header: bool = True,
    ) -> requests.Response | None:
        base_url = self.config.get_api_full_base_url()
        full_url = base_url + "/" + relative_path.strip('/')
        base_headers = {
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Content-Type": "application/json;charset=utf-8",
            "Origin": base_url,
            "Referer": base_url,
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        if add_auth_header:
            if not self.config.token_id:
                print(f"Warning: Auth header requested for {method} {full_url} but token_id is not set.")
            else:
                base_headers["Authorization"] = f"Bearer {self.config.token_id}"

        # Merge base headers with provided headers, giving precedence to provided headers
        full_headers = {**base_headers, **(headers or {})}

        if json is not None and "Content-Type" not in full_headers:
            full_headers["Content-Type"] = "application/json;charset=utf-8"

        response = None
        try:
            response = requests.request(
                method=method,
                url=full_url,
                params=params,
                data=data,
                headers=full_headers,
                cookies=cookies,
                files=files,
                auth=auth,
                timeout=timeout,
                allow_redirects=allow_redirects,
                proxies=proxies,
                hooks=hooks,
                stream=stream,
                verify=verify,
                cert=cert,
                json=json, # Pass the dict directly to requests
            )

            # print(f"Request: {method} {full_url}")
            # print(f"Response: {response.status_code} {response.reason}")
            # print(f"Response Content: {response.content}")
            # print(f"Response Text: {response.text}")

        except requests.exceptions.ConnectionError as e:
            # print(f"Error: Failed to connect to AnyShare server ({method} {full_url}): {e}")
            # return None # Return None for connection errors
            raise AnyshareApiException(
                message=f"Failed to connect to AnyShare server ({method} {full_url})",
                location="BaseApi.auth_request",
                original_exception=e
            ) from e
        except requests.exceptions.Timeout as e:
            # print(f"Error: Request timed out ({method} {full_url}): {e}")
            # return None # Return None for timeouts
            raise AnyshareApiException(
                message=f"Request timed out ({method} {full_url})",
                location="BaseApi.auth_request",
                original_exception=e
            ) from e
        except requests.exceptions.RequestException as e:
            error_msg = f"Request exception occurred for {method} {full_url}: {str(e)}"
            # print(error_msg)
            # If the exception has a response attached (like HTTPError), return it
            # if hasattr(e, 'response') and e.response is not None:
            #     return e.response
            # return None
            raise AnyshareApiException(
                message=error_msg,
                location="BaseApi.auth_request",
                original_exception=e,
                details=f"Response status: {e.response.status_code}" if hasattr(e, 'response') and e.response is not None else None
            ) from e
        except Exception as e:
            # Catch any other unexpected errors during the request process
            error_msg = f"An unexpected error occurred during the request to {method} {full_url}: {str(e)}"
            # print(error_msg)
            # return None
            raise AnyshareApiException(
                message=error_msg,
                location="BaseApi.auth_request",
                original_exception=e
            ) from e
        return response

    def api_request(self, 
      relative_path: str, 
      req_data: BaseModel = None, 
      success_model: Optional[type] = None, add_auth_header: bool = True
      ) -> Optional[Union[BaseModel, Error, str]]:
      data = None
      if req_data is not None:
          data = req_data.model_dump()

      # print(f"api_request: {relative_path}, {data}")
      response = self.auth_request(relative_path, "POST", json=data, add_auth_header=add_auth_header)
      
      # auth_request now raises AnyshareApiException directly on connection/timeout/etc.
      # So, response object should be valid here if no exception was raised by auth_request.
      # However, the original check `if response is None:` was there.
      # If auth_request could somehow still return None without an exception (which it shouldn't by design now),
      # this would be a fallback. Given the changes, this specific check might be overly defensive
      # if auth_request is guaranteed to raise or return a Response object.
      if response is None:
          raise AnyshareApiException(
              message="Received no response object from auth_request call.", # Should ideally not happen
              location="BaseApi.api_request",
              details=f"Path: {relative_path}"
          )
      
      if response.status_code == 200:
          if not response.text.strip(): # HTTP 200 OK with empty or whitespace-only body
              if success_model is None:
                  # Valid success with no content expected by the caller of api_request.
                  # Returning None here allows _handle_api_response in AnyshareClient
                  # to correctly interpret this as _SUCCESS_NO_CONTENT if success_model_type was type(None).
                  return None 
              else:
                  # HTTP 200 OK with empty body, but a specific model was expected.
                  raise AnyshareApiException(
                      message=f"Received 200 OK with empty body, but expected model '{success_model.__name__}'.",
                      location="BaseApi.api_request",
                      details=f"Path: {relative_path}, Status: {response.status_code}"
                  )
          else: # HTTP 200 OK with a non-empty body
              try:
                  response_json = response.json()
              except ValueError as e: # Includes JSONDecodeError
                  raise AnyshareApiException(
                      message="Failed to decode 200 OK response (with non-empty body) as JSON.",
                      location="BaseApi.api_request",
                      details=f"Path: {relative_path}, Response Text: {response.text[:200]}...",
                      original_exception=e
                  ) from e
            
              if success_model:
                  if isinstance(response_json, list):
                      try:
                          # Assuming success_model is the type of items in the list
                          return [success_model(**item) for item in response_json]
                      except Exception as e_parse_list:
                          raise AnyshareApiException(
                              message=f"Failed to parse 200 OK response list into '{success_model.__name__}' objects.",
                              location="BaseApi.api_request",
                              details=f"Path: {relative_path}, Data: {str(response_json)[:200]}...",
                              original_exception=e_parse_list
                          ) from e_parse_list
                  elif isinstance(response_json, dict):
                      try:
                          return success_model(**response_json)
                      except Exception as e_parse_dict:
                          raise AnyshareApiException(
                              message=f"Failed to parse 200 OK response dictionary into '{success_model.__name__}' object.",
                              location="BaseApi.api_request",
                              details=f"Path: {relative_path}, Data: {str(response_json)[:200]}...",
                              original_exception=e_parse_dict
                          ) from e_parse_dict
                  else:
                      raise AnyshareApiException(
                          message=f"Expected JSON list or dict for '{success_model.__name__}' parsing from 200 OK response, got {type(response_json)}.",
                          location="BaseApi.api_request",
                          details=f"Path: {relative_path}, Data: {str(response_json)[:200]}..."
                      )
              else: # success_model is None, but we have a non-empty JSON body. Return raw JSON.
                  return response_json
      else: # Non-200 status codes
          if not response.text.strip(): # Non-200 status with empty or whitespace-only body
              error_code_detail = ANYSHARE_ERROR_CODES_DETAILS.get(response.status_code) # Check if HTTP status itself is a known code
              msg_parts = [f"API Error: HTTP Status {response.status_code} with empty response body."]
              if error_code_detail:
                  msg_parts.append(f"Doc Msg: {error_code_detail.errmsg_doc}")
              raise AnyshareApiException(
                  message=" ".join(msg_parts),
                  location="BaseApi.api_request",
                  error_code=response.status_code,
                  details=f"Path: {relative_path}"
              )
          else: # Non-200 status with a non-empty body, try to parse as Error model
              try:
                  response_json = response.json()
                  error_obj = Error(**response_json)
                  
                  error_code_detail = ANYSHARE_ERROR_CODES_DETAILS.get(error_obj.errcode)
                  detailed_message_parts = [
                      f"API Error: HTTP Status {response.status_code}",
                      f"Error Code: {error_obj.errcode}",
                      f"Server Message: '{error_obj.errmsg}'",
                      f"Server Cause: '{error_obj.causemsg}'"
                  ]
                  if error_code_detail:
                      detailed_message_parts.append(f"Doc Msg: '{error_code_detail.errmsg_doc}'")
                      if error_code_detail.remarks: detailed_message_parts.append(f"Remarks: '{error_code_detail.remarks}'")
                      detailed_message_parts.append(f"Retry Suggestion: {error_code_detail.retry_suggestion}")
                      if error_code_detail.detailed_info_example: detailed_message_parts.append(f"Detail Example: '{error_code_detail.detailed_info_example}'")
                  else:
                      detailed_message_parts.append("(No additional details in local error code dictionary for this errcode)")
                  
                  raise AnyshareApiException(
                      message=", ".join(detailed_message_parts),
                      location="BaseApi.api_request",
                      error_code=error_obj.errcode,
                      details=f"Path: {relative_path}, Raw Error Response: {str(response_json)[:200]}..."
                  )
              except ValueError as e_json_non_200: # Includes JSONDecodeError for non-200 response
                  raise AnyshareApiException(
                      message=f"API Error: HTTP Status {response.status_code}. Failed to decode error response body as JSON.",
                      location="BaseApi.api_request",
                      error_code=response.status_code,
                      details=f"Path: {relative_path}, Response Text: {response.text[:200]}...",
                      original_exception=e_json_non_200
                  ) from e_json_non_200
              except Exception as e_parse_error_obj: # Pydantic validation error for Error model or other
                  # This might occur if response_json was obtained but couldn't be parsed into Error model
                  # It's good to include what response_json was if available.
                  parsed_json_for_details = "Could not obtain/parse response_json before this stage."
                  try: 
                      parsed_json_for_details = str(response.json())[:200] # Try to get it again for logging if not already
                  except: pass

                  raise AnyshareApiException(
                      message=f"API Error: HTTP Status {response.status_code}. Failed to parse JSON error response into Error model.",
                      location="BaseApi.api_request",
                      error_code=response.status_code,
                      details=f"Path: {relative_path}, Parsed/Raw Response: {parsed_json_for_details}...",
                      original_exception=e_parse_error_obj
                  ) from e_parse_error_obj