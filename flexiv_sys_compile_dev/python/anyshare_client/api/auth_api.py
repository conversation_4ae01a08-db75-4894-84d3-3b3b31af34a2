#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
from typing import Optional, Union

from .base_api import Base<PERSON>pi
from .models import *
from anyshare_client.config import Config

class AuthApi(BaseApi):
    def __init__(self, config: Config):
        super().__init__(config)
    
    def _encrypt_text(self, text: str) -> str | None:
        try:
            # pub_key_path = self.config.anyshare_pub_key_path
            # with open(pub_key_path, 'r') as f:
            #     key_data = f.read()
            key_data = (
                "-----BEGIN PUBLIC KEY-----\n"
                "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7JL0DcaMUHumSdhxXTxqiABBC\n"
                "DERhRJIsAPB++zx1INgSEKPGbexDt1ojcNAc0fI+G/yTuQcgH1EW8posgUni0mcT\n"
                "E6CnjkVbv8ILgCuhy+4eu+2lApDwQPD9Tr6J8k21Ruu2sWV5Z1VRuQFqGm/c5vaT\n"
                "OQE5VFOIXPVTaa25mQIDAQAB\n"
                "-----END PUBLIC KEY-----"
            )
            key = RSA.importKey(key_data)
            cipher = PKCS1_v1_5.new(key)
            encrypted_bytes = cipher.encrypt(text.encode('utf-8'))
            return base64.b64encode(encrypted_bytes).decode('utf-8')
        except Exception as e:
            print(f"Encryption error: {e}")
            return None
        
    def auth_get_new(self, req_data: Auth1GetnewReq) -> Optional[Union[Auth1GetnewRes, Error, str]]:
        return self.api_request(relative_path="/auth1/getnew/", req_data=req_data, success_model=Auth1GetnewRes, add_auth_header=False)

    def auth_logout(self, req_data: Auth1LogoutReq) -> Optional[Union[Error, str]]:
        return self.api_request(relative_path="auth1/logout", req_data=req_data, success_model=None)
    def login(self, username, password):
        encrypted_password = self._encrypt_text(password)
        if not encrypted_password:
            print("Login failed: Password encryption step failed.")
            return False

        auth_getnew_req = Auth1GetnewReq(
            account=username,
            password=encrypted_password
        )
        response_data = self.auth_get_new(auth_getnew_req)
        if isinstance(response_data, Auth1GetnewRes):
            self.config.token_id = response_data.tokenid
            self.config.user_id = response_data.userid
            self.config.token_id_expires = response_data.expires
            if self.config.token_id:
                print(f"Login success.")
                print(f"User ID: {self.config.user_id}")
                print(f"Token expires: {self.config.token_id_expires}")
                print(f"Login token: {self.config.token_id}")
                return True
            else:
                print(f"Login failed: 'tokenid' not found in response. Response: {response_data}")
                return False
        else:
            print(f"Login failed. Response from server: {response_data}")
            return False

    def logout(self):
        if not self.config.token_id:
            print("Logout skipped: No active session (token_id is not set).")
            return False 

        auth_logout_req = Auth1LogoutReq(ostype=0)
        response_data = self.auth_logout(auth_logout_req)

        if isinstance(response_data, Error):
            print("Logout failed:", response_data)
            return False
        else:
            if response_data is not None:
                print(type(response_data))
                print("Logout response:", response_data)
            self.config.token_id = ""
            self.config.user_id = ""
            self.config.token_id_expires = ""
            print("Logout success.")
            return True
