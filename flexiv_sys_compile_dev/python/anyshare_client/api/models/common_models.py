from pydantic import BaseModel, Field
from typing import Dict, Optional, Union, Any

class Error(BaseModel):
    errcode: int = Field(description="错误码")
    errmsg: str = Field(description="错误描述，与错误码对应")
    causemsg: str = Field(description="错误原因，底层错误信息，仅用于研发排错，客户端不应使用此字段内容")

class ErrorCodeDetail(BaseModel):
    http_status: int
    errmsg_doc: str # 对应文档中的"错误信息"
    remarks: Optional[str] = None # 对应文档中的"备注"
    detailed_info_example: Optional[str] = None # 对应文档中的"错误附加详细信息"
    retry_suggestion: bool = False # 对应文档中的"建议重试"

# Parsed from 错误码说明.json - 异常错误码对照表
ANYSHARE_ERROR_CODES_DETAILS: Dict[int, ErrorCodeDetail] = {
    400001: ErrorCodeDetail(http_status=400, errmsg_doc="URI not exists", remarks="URI 资源不存在", retry_suggestion=False),
    400002: ErrorCodeDetail(http_status=400, errmsg_doc="illegal parameters", remarks="非法参数", retry_suggestion=False),
    400003: ErrorCodeDetail(http_status=400, errmsg_doc="uri format error", remarks="Uri 格式错误或者设置的method不支持", retry_suggestion=False),
    400004: ErrorCodeDetail(http_status=400, errmsg_doc="json format error", remarks="Json格式错误", retry_suggestion=False),
    400005: ErrorCodeDetail(http_status=400, errmsg_doc="illegal item of \"permconfig\"", remarks="非法permconfigs项", retry_suggestion=False),
    400007: ErrorCodeDetail(http_status=400, errmsg_doc="data block length not matched", remarks="数据块大小和实际大小不匹配", retry_suggestion=False),
    400008: ErrorCodeDetail(http_status=400, errmsg_doc="search syntax errors, special characters or error code", remarks="全文检索语法错误，特殊字符或错误编码", retry_suggestion=False),
    400009: ErrorCodeDetail(http_status=400, errmsg_doc="invalid request search parameter", remarks="全文检索请求查询的参数无效", retry_suggestion=False),
    400010: ErrorCodeDetail(http_status=400, errmsg_doc="time setting is earlier than current server time", remarks="您设置的时间已经早于服务器的当前时间，请重新设置或校对时间。", retry_suggestion=False),
    400011: ErrorCodeDetail(http_status=400, errmsg_doc="valid period setting cannot be more than 5 years", remarks="您设置的有效期已经超过了5年的限制，请重新设置或校对时间。", retry_suggestion=False),
    400012: ErrorCodeDetail(http_status=400, errmsg_doc="Name cannot contain any of the following characters: \\/:*?\"<>|", remarks="名称不能包含下列任何字符： \\/:*?\"<>|", retry_suggestion=False),
    400013: ErrorCodeDetail(http_status=400, errmsg_doc="Invalid group doc nam", remarks="群组名不合法，可能字符过长或包含\\ / : \* ? \" \< \> \| 特殊字符。", retry_suggestion=False),
    400014: ErrorCodeDetail(http_status=400, errmsg_doc="Tag cannot contain any of the following characters: \\/:*?\"<>|#", remarks="标签不能包含下列任何字符： \\/:*?\"<>|#", retry_suggestion=False),
    400015: ErrorCodeDetail(http_status=400, errmsg_doc="The permission expiration you set is earlier than the current time of server", remarks="权限有效期限已经早于服务器的当前时间", retry_suggestion=False),
    400016: ErrorCodeDetail(http_status=400, errmsg_doc="The link expiration you set is earlier than the current time of server", remarks="链接有效期限已经早于服务器的当前时间", retry_suggestion=False),
    400017: ErrorCodeDetail(http_status=400, errmsg_doc="The expiration date of link should be earlier than that of permission.", remarks="链接有效期限不能大于权限有效期限", retry_suggestion=False),
    400018: ErrorCodeDetail(http_status=400, errmsg_doc="The displayName cannot be empty", remarks="显示名不能为空", retry_suggestion=False),
    401001: ErrorCodeDetail(http_status=401, errmsg_doc="Access Token Check failure", remarks="Access Token校验失败", retry_suggestion=False),
    401002: ErrorCodeDetail(http_status=401, errmsg_doc="Incorrect link password.", remarks="外链密码不正确。", retry_suggestion=False),
    401003: ErrorCodeDetail(http_status=401, errmsg_doc="invalid user or password", remarks="用户名或者密码不正确", retry_suggestion=False),
    401004: ErrorCodeDetail(http_status=401, errmsg_doc="user is disabled", remarks="用户已被禁用", retry_suggestion=False),
    401005: ErrorCodeDetail(http_status=401, errmsg_doc="invalid encryption credential", remarks="加密凭证无效", retry_suggestion=False),
    401006: ErrorCodeDetail(http_status=401, errmsg_doc="System account not allowed to login in", remarks="系统帐号不允许登录Client", retry_suggestion=False),
    401007: ErrorCodeDetail(http_status=401, errmsg_doc="Domain disabled or deleted", remarks="域控被禁用或删除", retry_suggestion=False),
    401008: ErrorCodeDetail(http_status=401, errmsg_doc="User not exist in domain", remarks="用户不存在于域控", retry_suggestion=False),
    401009: ErrorCodeDetail(http_status=401, errmsg_doc="Device is disabled", remarks="登录设备已被禁用", retry_suggestion=False),
    401010: ErrorCodeDetail(http_status=401, errmsg_doc="Device is erased", remarks="登录设备数据需要擦除", retry_suggestion=False),
    401011: ErrorCodeDetail(http_status=401, errmsg_doc="Account binded with other device", remarks="该帐号仅支持在绑定设备上登录", retry_suggestion=False),
    401012: ErrorCodeDetail(http_status=401, errmsg_doc="password expire", remarks="密码失效", retry_suggestion=False),
    401013: ErrorCodeDetail(http_status=401, errmsg_doc="password not safe", remarks="密码不安全", retry_suggestion=False),
    401014: ErrorCodeDetail(http_status=401, errmsg_doc="Invalid password", remarks="无效的普通密码", retry_suggestion=False),
    401015: ErrorCodeDetail(http_status=401, errmsg_doc="Invalid strong password", remarks="无效的强密码", retry_suggestion=False),
    401016: ErrorCodeDetail(http_status=401, errmsg_doc="Not local user", remarks="非本地用户不能修改密码", retry_suggestion=False),
    401017: ErrorCodeDetail(http_status=401, errmsg_doc="Password is initial", remarks="用户密码为初始密码", retry_suggestion=False),
    401018: ErrorCodeDetail(http_status=401, errmsg_doc="Password first failed", remarks="密码验证第一次失败", retry_suggestion=False),
    401019: ErrorCodeDetail(http_status=401, errmsg_doc="Password second failed", remarks="密码验证第二次失败", retry_suggestion=False),
    401020: ErrorCodeDetail(http_status=401, errmsg_doc="Account has been locked", remarks="账号已被锁定", detailed_info_example="detail: {\"remainlockTime\": 2} (统一以分钟为单位)", retry_suggestion=False),
    401021: ErrorCodeDetail(http_status=401, errmsg_doc="license not actived", remarks="用户尚未授权", retry_suggestion=False),
    401022: ErrorCodeDetail(http_status=401, errmsg_doc="Failed to connect domain server", remarks="连接域服务器失败", retry_suggestion=False),
    401023: ErrorCodeDetail(http_status=401, errmsg_doc="User not exist", remarks="用户不存在", retry_suggestion=False),
    401024: ErrorCodeDetail(http_status=401, errmsg_doc="Wrong password", remarks="用户密码错误", retry_suggestion=False),
    401025: ErrorCodeDetail(http_status=401, errmsg_doc="Force log off", remarks="客户端被强制下线（该帐号在其它客户端登录）", retry_suggestion=False),
    401026: ErrorCodeDetail(http_status=401, errmsg_doc="Cannot modify control password", remarks="开启密码管控的用户不能修改密码", retry_suggestion=False),
    401027: ErrorCodeDetail(http_status=401, errmsg_doc="Control password expire", remarks="开启密码管控的用户密码过期", retry_suggestion=False),
    401028: ErrorCodeDetail(http_status=401, errmsg_doc="Can not login slave site", remarks="当前站点为分站点模式，无法登录", retry_suggestion=False),
    401029: ErrorCodeDetail(http_status=401, errmsg_doc="license has expired", remarks="用户授权过期", retry_suggestion=False),
    401030: ErrorCodeDetail(http_status=401, errmsg_doc="Reach access limit", remarks="已达外链使用次数上限。", retry_suggestion=False),
    401031: ErrorCodeDetail(http_status=401, errmsg_doc="Your account is not allowed to login in this IP segment anymore", remarks="您的账号受到IP 网段的限制，无法继续登录", retry_suggestion=False),
    401032: ErrorCodeDetail(http_status=401, errmsg_doc="Account has been locked in secret", remarks="涉密模式下，账号已被锁定", retry_suggestion=False),
    401033: ErrorCodeDetail(http_status=401, errmsg_doc="The os type has been forbidded", remarks="管理员已禁止此类客户端登录。", retry_suggestion=False),
    401034: ErrorCodeDetail(http_status=401, errmsg_doc="Refresh token invalid or expired", remarks="刷新token不存在或已过期", retry_suggestion=False),
    401035: ErrorCodeDetail(http_status=401, errmsg_doc="New password should not be default password.", remarks="修改密码时，新密码不能为默认密码。", retry_suggestion=False),
    401037: ErrorCodeDetail(http_status=401, errmsg_doc="Please enter verification code", remarks="请输入验证码", retry_suggestion=False),
    401038: ErrorCodeDetail(http_status=401, errmsg_doc="Invalid verification code", remarks="输入的验证码已失效", retry_suggestion=False),
    401039: ErrorCodeDetail(http_status=401, errmsg_doc="Incorrect verification code", remarks="输入的验证码有误", retry_suggestion=False),
    401040: ErrorCodeDetail(http_status=401, errmsg_doc="User Disabled, please activate", remarks="用户已禁用，请激活", retry_suggestion=False),
    401041: ErrorCodeDetail(http_status=401, errmsg_doc="User is activate", remarks="用户已激活，请登录", retry_suggestion=False),
    401042: ErrorCodeDetail(http_status=401, errmsg_doc="Invalid telphone number", remarks="手机号不合法", retry_suggestion=False),
    401043: ErrorCodeDetail(http_status=401, errmsg_doc="Telphone exists", remarks="手机号已存在", retry_suggestion=False),
    401044: ErrorCodeDetail(http_status=401, errmsg_doc="Incorrect sms verification code", remarks="短信验证码不正确", retry_suggestion=False),
    401045: ErrorCodeDetail(http_status=401, errmsg_doc="Invalid sms verification code", remarks="短信验证码过期", retry_suggestion=False),
    401046: ErrorCodeDetail(http_status=401, errmsg_doc="Send sms error", remarks="发送验证码失败", retry_suggestion=False),
    401047: ErrorCodeDetail(http_status=401, errmsg_doc="Sms activate disabled", remarks="短信激活未开启", retry_suggestion=False),
    401048: ErrorCodeDetail(http_status=401, errmsg_doc="Invalid email", remarks="邮箱不合法", retry_suggestion=False),
    401049: ErrorCodeDetail(http_status=401, errmsg_doc="Email exists", remarks="邮箱已存在", retry_suggestion=False),
    401050: ErrorCodeDetail(http_status=401, errmsg_doc="User acticate error", remarks="激活失败", retry_suggestion=False),
    401051: ErrorCodeDetail(http_status=401, errmsg_doc="管理員已關閉身份證號登錄，請重新登錄", remarks="管理员已关闭身份证号登录，请重新登录", retry_suggestion=False),
    401052: ErrorCodeDetail(http_status=401, errmsg_doc="illegal user name", remarks="用户显示名不合法", retry_suggestion=False),
    401053: ErrorCodeDetail(http_status=401, errmsg_doc="User name already exists", remarks="用户显示名已被占用", retry_suggestion=False),
    403001: ErrorCodeDetail(http_status=403, errmsg_doc="quota is not enough", remarks="空间配额不足", retry_suggestion=False),
    403002: ErrorCodeDetail(http_status=403, errmsg_doc="no permission to do this operation", remarks="没有权限执行此操作", retry_suggestion=False),
    403003: ErrorCodeDetail(http_status=403, errmsg_doc="only owner can do this operation", remarks="只有文档所有者才能进行该操作", retry_suggestion=False),
    403004: ErrorCodeDetail(http_status=403, errmsg_doc="set personal doc owner is not allowed", remarks="个人文档不能设置所有者", retry_suggestion=False),
    403005: ErrorCodeDetail(http_status=403, errmsg_doc="add self or set administrator as an owner is not allowed", remarks="不能添加自己或设置管理员为所有者", retry_suggestion=False),
    403006: ErrorCodeDetail(http_status=403, errmsg_doc="delete the config of your own is not allowed", remarks="不能删除自己的所有者配置", retry_suggestion=False),
    403007: ErrorCodeDetail(http_status=403, errmsg_doc="user cannot create more than three groups", remarks="个人创建的群组个数不能超过3个", retry_suggestion=False),
    403008: ErrorCodeDetail(http_status=403, errmsg_doc="visitor not exist when set the permission", remarks="配权限时，访问者不存在", retry_suggestion=False),
    403009: ErrorCodeDetail(http_status=403, errmsg_doc="set user space quota to zero is not allowed", remarks="个人空间配额不能设置为0", retry_suggestion=False),
    403010: ErrorCodeDetail(http_status=403, errmsg_doc="group name already exists", remarks="群组名称已经存在", retry_suggestion=False),
    403011: ErrorCodeDetail(http_status=403, errmsg_doc="user not exist when set the permission", remarks="配所有者时，指定用户不存在", retry_suggestion=False),
    403012: ErrorCodeDetail(http_status=403, errmsg_doc="user already exists", remarks="用户已经存在", retry_suggestion=False),
    403013: ErrorCodeDetail(http_status=403, errmsg_doc="out link has already opened", remarks="外链已经存在", retry_suggestion=False),
    403014: ErrorCodeDetail(http_status=403, errmsg_doc="add data block to the completed file version is not allowed", remarks="不能向完整版本添加数据块", retry_suggestion=False),
    403015: ErrorCodeDetail(http_status=403, errmsg_doc="illegal object type for current operation", remarks="操作的对象类型不正确", retry_suggestion=False),
    403016: ErrorCodeDetail(http_status=403, errmsg_doc="delete file or directory that not in recycle bin is not allowed", remarks="不能删除不在回收站的文件或目录", retry_suggestion=False),
    403017: ErrorCodeDetail(http_status=403, errmsg_doc="recover file or directory that not in recycle bin is not allowed", remarks="不能还原不在回收站的文件或目录", retry_suggestion=False),
    403018: ErrorCodeDetail(http_status=403, errmsg_doc="download uncompleted file version is not allowed", remarks="不能下载非完整的版本对象", retry_suggestion=False),
    403019: ErrorCodeDetail(http_status=403, errmsg_doc="File or directory cannot be moved to the same or sub path", remarks="对象无法移动到相同的路径或者子路径", retry_suggestion=False),
    403020: ErrorCodeDetail(http_status=403, errmsg_doc="current document has already opened the Finding Sharing Operation failed", remarks="当前文档已经开启发现共享，导致操作失败。", retry_suggestion=False),
    403021: ErrorCodeDetail(http_status=403, errmsg_doc="parent directory has already opened the Finding Sharing Operation failed", remarks="父目录已经开启发现共享，导致操作失败。", retry_suggestion=False),
    403022: ErrorCodeDetail(http_status=403, errmsg_doc="delete an empty resource is not allowed", remarks="不能删除不存在的数据块。", retry_suggestion=False),
    403023: ErrorCodeDetail(http_status=403, errmsg_doc="file or thumbnail is too big to be converted", remarks="预览文档或缩略图过大，预览失败。", retry_suggestion=False),
    403024: ErrorCodeDetail(http_status=403, errmsg_doc="CID object not exists", remarks="管理对象不存在", retry_suggestion=False),
    403026: ErrorCodeDetail(http_status=403, errmsg_doc="file format error", remarks="文档预览失败，格式错误", retry_suggestion=False),
    403027: ErrorCodeDetail(http_status=403, errmsg_doc="image format error", remarks="缩略图预览失败，格式错误", retry_suggestion=False),
    403028: ErrorCodeDetail(http_status=403, errmsg_doc="oauth not enabled", remarks="OAuth认证未开启", retry_suggestion=False),
    403029: ErrorCodeDetail(http_status=403, errmsg_doc="can\'t authenticate ticket", remarks="无法验证ticket", retry_suggestion=False),
    403030: ErrorCodeDetail(http_status=403, errmsg_doc="user not import to anyshare", remarks="该用户未导入到AnyShare系统中", retry_suggestion=False),
    403031: ErrorCodeDetail(http_status=403, errmsg_doc="file is locked", remarks="文档对象被锁定", retry_suggestion=False),
    403032: ErrorCodeDetail(http_status=403, errmsg_doc="file is not locked", remarks="文件未锁定", retry_suggestion=False),
    403033: ErrorCodeDetail(http_status=403, errmsg_doc="only owner or locker cant unlock the file", remarks="只有文件所有者或文件锁定者才能解锁文件", retry_suggestion=False),
    403034: ErrorCodeDetail(http_status=403, errmsg_doc="can\'t lock directory", remarks="不能对文件夹加锁", retry_suggestion=False),
    403035: ErrorCodeDetail(http_status=403, errmsg_doc="version size mismatch in upload", remarks="尝试秒传写入的大小与实际上传的大小不匹配", retry_suggestion=False),
    403036: ErrorCodeDetail(http_status=403, errmsg_doc="Invalid storagesite in version metadata", remarks="版本元数据中存储字段无效", retry_suggestion=False),
    403037: ErrorCodeDetail(http_status=403, errmsg_doc="version size mismatch in hash", remarks="计算Hash时，标识的大小与实际大小不匹配", retry_suggestion=False),
    403038: ErrorCodeDetail(http_status=403, errmsg_doc="Uploaded file is incomplete", remarks="上传的文件不完整", retry_suggestion=True),
    403039: ErrorCodeDetail(http_status=403, errmsg_doc="Exist same type and same name file", remarks="存在同类型的同名文件名", retry_suggestion=False),
    403040: ErrorCodeDetail(http_status=403, errmsg_doc="Exist same type and same name file，but no edit permission", remarks="存在同类型的同名文件，但无修改权限", retry_suggestion=False),
    403041: ErrorCodeDetail(http_status=403, errmsg_doc="Exist different type and same name file", remarks="存在不同类型的同名文件", retry_suggestion=False),
    403042: ErrorCodeDetail(http_status=403, errmsg_doc="Open preview file failed.", remarks="预览缓存文件打开失败", retry_suggestion=False),
    403043: ErrorCodeDetail(http_status=403, errmsg_doc="Current operating can not support overwrite.", remarks="当前操作不支持覆盖", retry_suggestion=False),
    403044: ErrorCodeDetail(http_status=403, errmsg_doc="current document has not opened the Finding Sharing", remarks="当前文档未开启发现共享。", retry_suggestion=False),
    403045: ErrorCodeDetail(http_status=403, errmsg_doc="please get to third authentication website to login", remarks="请通过第三方认证网站进行登录", retry_suggestion=False),
    403046: ErrorCodeDetail(http_status=403, errmsg_doc="data block requested not exists", remarks="请求对应数据块不存在", retry_suggestion=False),
    403047: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to transcode onlineplay resource, unsupported file format", remarks="转码在线播放资源失败，文件格式不支持", retry_suggestion=False),
    403048: ErrorCodeDetail(http_status=403, errmsg_doc="object mismatches the link", remarks="指定对象和外链不匹配", retry_suggestion=False),
    403049: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to handle encrypted content", remarks="处理加密内容失败", retry_suggestion=False),
    403050: ErrorCodeDetail(http_status=403, errmsg_doc="Windows ad sso not enabled", remarks="域用户自动登录未开启", retry_suggestion=False),
    403051: ErrorCodeDetail(http_status=403, errmsg_doc="You can only quit on entry document.", remarks="只能针对入口文档退出共享", retry_suggestion=False),
    403052: ErrorCodeDetail(http_status=403, errmsg_doc="You can\'t access the document, can\'t quit.", remarks="无法访问到该入口文档，不能进行此操作", retry_suggestion=False),
    403053: ErrorCodeDetail(http_status=403, errmsg_doc="You can\'t quit the user document.", remarks="不能针对个人文档进行退出共享管理", retry_suggestion=False),
    403054: ErrorCodeDetail(http_status=403, errmsg_doc="File or directory cannot be copied to sub path", remarks="对象无法复制到子路径", retry_suggestion=False),
    403055: ErrorCodeDetail(http_status=403, errmsg_doc="Check backup file error", remarks="校验备份文件失败", retry_suggestion=False),
    403056: ErrorCodeDetail(http_status=403, errmsg_doc="no permission to operate destination object", remarks="沒有权限操作目标位置的对象", retry_suggestion=False),
    403057: ErrorCodeDetail(http_status=403, errmsg_doc="Third server is unavailable", remarks="第三方服务器认证失败", retry_suggestion=False),
    403058: ErrorCodeDetail(http_status=403, errmsg_doc="Conflicated with document library.", remarks="群组文档名称与文档库名称冲突", retry_suggestion=False),
    403059: ErrorCodeDetail(http_status=403, errmsg_doc="Check upload file size error.", remarks="校验上传文件的大小失败", retry_suggestion=True),
    403060: ErrorCodeDetail(http_status=403, errmsg_doc="Cant not add edit or delete permission to archive doc.", remarks="不能给归档库配置修改和删除权限", retry_suggestion=False),
    403061: ErrorCodeDetail(http_status=403, errmsg_doc="Invalid EndTime", remarks="您设置的时间已经早于服务器的当前时间，请重新设置或校对时间", retry_suggestion=False),
    403062: ErrorCodeDetail(http_status=403, errmsg_doc="Find share is not unavailable", remarks="没有发现共享权限", retry_suggestion=False),
    403063: ErrorCodeDetail(http_status=403, errmsg_doc="User has no Link share permission", remarks="没有外链共享权限", retry_suggestion=False),
    403064: ErrorCodeDetail(http_status=403, errmsg_doc="User cannot set a classified value higher than his classified level", remarks="用户设置的密级不能高于自身密级", retry_suggestion=False),
    403065: ErrorCodeDetail(http_status=403, errmsg_doc="User classified level is lower than doc classified level, operation is stopped", remarks="用户密级低于文件密级,无法操作", retry_suggestion=False),
    403066: ErrorCodeDetail(http_status=403, errmsg_doc="Authorized user classified level is lower than doc classified level", remarks="您指定的用户密级低于当前文件的密级，无法对其配置共享。", retry_suggestion=False),
    403067: ErrorCodeDetail(http_status=403, errmsg_doc="Conflicated with user displayname", remarks="群组文档名称与用户显示名冲突", retry_suggestion=False),
    403068: ErrorCodeDetail(http_status=403, errmsg_doc="Conflicated with archive lib name", remarks="群组文档名称与归档库名冲突", retry_suggestion=False),
    403069: ErrorCodeDetail(http_status=403, errmsg_doc="Transcoding failed, server cache space is not enough, please contact administrator.", remarks="转码失败，服务器缓存空间不足，请联系管理员。", retry_suggestion=False),
    403070: ErrorCodeDetail(http_status=403, errmsg_doc="Large file transmission is limited in current network", remarks="所在网络受到限制,无法传输大文件", retry_suggestion=False),
    403092: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to add tag, the tag\'s maximum limit has been reached.", remarks="添加标签失败，标签数量已达到上限", retry_suggestion=False),
    403093: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to add tag, the tag already exists.", remarks="添加标签失败，标签已存在", retry_suggestion=False),
    403094: ErrorCodeDetail(http_status=403, errmsg_doc="No auditor.", remarks="当前无匹配的审核员，本次操作无法生效，请联系管理员。", retry_suggestion=False),
    403095: ErrorCodeDetail(http_status=403, errmsg_doc="Apply is invalid.", remarks="此条申请已失效或已被其他审核员审核完成。", retry_suggestion=False),
    403096: ErrorCodeDetail(http_status=403, errmsg_doc="Not allowed to approve.", remarks="您的审核权限已失效。", retry_suggestion=False),
    403097: ErrorCodeDetail(http_status=403, errmsg_doc="Apply is invalid cause accessor not exists.", remarks="此条申请已失效（访问者已经被删除）", retry_suggestion=False),
    403098: ErrorCodeDetail(http_status=403, errmsg_doc="Invalid approve deny message.", remarks="否决申请时，内容不能为空", retry_suggestion=False),
    403099: ErrorCodeDetail(http_status=403, errmsg_doc="Apply is invalid cause sharer not exists.", remarks="此条申请已失效（共享者已经被删除）", retry_suggestion=False),
    403100: ErrorCodeDetail(http_status=403, errmsg_doc="Apply is invalid cause end time expired.", remarks="此条申请已失效（截至时间已经过期）", retry_suggestion=False),
    403101: ErrorCodeDetail(http_status=403, errmsg_doc="Audit policy is enabled, Cant use old perm protocol.", remarks="共享审核已经开启，请使用set协议来进行共享。", retry_suggestion=False),
    403102: ErrorCodeDetail(http_status=403, errmsg_doc="Apply is invalid cause link has been invalid.", remarks="此条申请已失效（外链已失效）", retry_suggestion=False),
    403103: ErrorCodeDetail(http_status=403, errmsg_doc="Apply is invalid cause link already exists.", remarks="此条申请已失效（外链已存在）", retry_suggestion=False),
    403104: ErrorCodeDetail(http_status=403, errmsg_doc="Upload failed, insufficent physical disk space, please contact admin.", remarks="无法执行上传操作，物理磁盘剩余空间不足，请联系管理员", retry_suggestion=False),
    403105: ErrorCodeDetail(http_status=403, errmsg_doc="Document is being audited.", remarks="当前文档已处于待审核状态，无法发起其他文档流程", retry_suggestion=False),
    403106: ErrorCodeDetail(http_status=403, errmsg_doc="get ext_app failed", remarks="登录外部应用失败", retry_suggestion=False),
    403107: ErrorCodeDetail(http_status=403, errmsg_doc="Can not share causeauditor insufficient csf level", remarks="当前审核员密级不足，本次操作无法生效，请联系管理员", retry_suggestion=False),
    403108: ErrorCodeDetail(http_status=403, errmsg_doc="Can not audit cause auditor insufficient csf level.", remarks="您的密级不足", retry_suggestion=False),
    403109: ErrorCodeDetail(http_status=403, errmsg_doc="Published document not exist.", remarks="发起流程失败，发布的文档不存在", retry_suggestion=False),
    403110: ErrorCodeDetail(http_status=403, errmsg_doc="Document process not exist.", remarks="文档流程不存在", retry_suggestion=False),
    403111: ErrorCodeDetail(http_status=403, errmsg_doc="Invalid application message.", remarks="发起流程失败，申请理由不能超过1024个字节", retry_suggestion=False),
    403112: ErrorCodeDetail(http_status=403, errmsg_doc="Invalid audit message.", remarks="审核流程失败，补充说明不能超过1024个字节", retry_suggestion=False),
    403113: ErrorCodeDetail(http_status=403, errmsg_doc="Cant publish cause permission denied.", remarks="无法执行发起流程操作，您对文件/文件夹没有修改权限。", retry_suggestion=False),
    403114: ErrorCodeDetail(http_status=403, errmsg_doc="Cant publish cause insufficient CSF level.", remarks="发起流程失败，您的密级小于文档密级", retry_suggestion=False),
    403115: ErrorCodeDetail(http_status=403, errmsg_doc="Cant publish CID object.", remarks="发起流程失败，不能对CID对象进行流程操作。", retry_suggestion=False),
    403116: ErrorCodeDetail(http_status=403, errmsg_doc="Cant publish, insufficient auditor CSF level.", remarks="发起流程失败，审核员密级不足", retry_suggestion=False),
    403117: ErrorCodeDetail(http_status=403, errmsg_doc="Cant publis, process is invalid.", remarks="发起流程失败，流程已失效", retry_suggestion=False),
    403118: ErrorCodeDetail(http_status=403, errmsg_doc="Cant approve, application is invalid", remarks="该流程申请已失效。", retry_suggestion=False),
    403119: ErrorCodeDetail(http_status=403, errmsg_doc="Cant approve, document not exists.", remarks="无法进行审批（文档已不存在）。", retry_suggestion=False),
    403120: ErrorCodeDetail(http_status=403, errmsg_doc="Cant approve, src document conflict with dest document.", remarks="无法进行审批（源文档路径与目的路径冲突）", retry_suggestion=False),
    403121: ErrorCodeDetail(http_status=403, errmsg_doc="Not set dst doc name for doc exchange process", remarks="内外网数据交换流程中未设置目的路径", retry_suggestion=False),
    403122: ErrorCodeDetail(http_status=403, errmsg_doc="Cant publist doc exchange process", remarks="当前目录不允许发起内外网数据交换流程", retry_suggestion=False),
    403123: ErrorCodeDetail(http_status=403, errmsg_doc="Doc exchange process not exist", remarks="内外网数据交换流程不存在", retry_suggestion=False),
    403124: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to connect to cross-network file transfer processing service", remarks="无法连到内外网数据交换处理服务", retry_suggestion=False),
    403125: ErrorCodeDetail(http_status=403, errmsg_doc="Replace failed, you cannot modify files in Archive Library", remarks="无法执行覆盖操作，您无法修改归档库的文件", retry_suggestion=False),
    403126: ErrorCodeDetail(http_status=403, errmsg_doc="Cross-network segment file transfer service has not been registered", remarks="内外网数据交换处理服务未注册", retry_suggestion=False),
    403127: ErrorCodeDetail(http_status=403, errmsg_doc="Uninstall password has been disabled", remarks="卸载口令没有正常启用", retry_suggestion=False),
    403128: ErrorCodeDetail(http_status=403, errmsg_doc="Incorrect password", remarks="口令不正确", retry_suggestion=False),
    403129: ErrorCodeDetail(http_status=403, errmsg_doc="Group Documents is not empty.", remarks="群组文档不为空", retry_suggestion=False),
    403130: ErrorCodeDetail(http_status=403, errmsg_doc="Operation failed, maybe some custom attribute has been deleted by admin.", remarks="无法执行此操作，可能某个自定义属性已被管理员删除。", retry_suggestion=False),
    403131: ErrorCodeDetail(http_status=403, errmsg_doc="The length of text content in custom file attribute is too long", remarks="自定义文件属性的文本长度超过上限", retry_suggestion=False),
    403132: ErrorCodeDetail(http_status=403, errmsg_doc="Type of custom file attribute value does not match.", remarks="自定义文件属性值类型不匹配", retry_suggestion=False),
    403133: ErrorCodeDetail(http_status=403, errmsg_doc="Value of custom file attribute does not exist", remarks="自定义文件属性值不存在", retry_suggestion=False),
    403142: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to submit file comment, please confirm file comment is enabled", remarks="发表失败，请确认评论功能是否正常启用", retry_suggestion=False),
    403143: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to delete file comment, cannot score a file repeatedly", remarks="删除失败，请确认评论功能是否正常启用", retry_suggestion=False),
    403144: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to submit file comment, cannot score a file repeatedly", remarks="发表失败，不能对同一文件重复评分", retry_suggestion=False),
    403145: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to delete file comment, only can delete own comment", remarks="删除失败，只能删除自己发表的评论", retry_suggestion=False),
    403146: ErrorCodeDetail(http_status=403, errmsg_doc="Exceed the inner link template maximum permissions", remarks="超出内链模板可设定最大权限", retry_suggestion=False),
    403147: ErrorCodeDetail(http_status=403, errmsg_doc="Exceed the inner link template maximum expire date", remarks="超过内链模板可设定最大有效期", retry_suggestion=False),
    403148: ErrorCodeDetail(http_status=403, errmsg_doc="Exceed the outer link template maximum permissions", remarks="超出外链模板可设定最大权限", retry_suggestion=False),
    403149: ErrorCodeDetail(http_status=403, errmsg_doc="Exceed the outer link template maximum expire date", remarks="超过外链模板可设定最大有效期", retry_suggestion=False),
    403150: ErrorCodeDetail(http_status=403, errmsg_doc="Exceed the outer link template maximum access times", remarks="超过外链模板可设定最大访问次数", retry_suggestion=False),
    403151: ErrorCodeDetail(http_status=403, errmsg_doc="The outer link shared password is not set", remarks="外链共享密码未设置", retry_suggestion=False),
    403152: ErrorCodeDetail(http_status=403, errmsg_doc="You are not allowed to set the validity as permanent", remarks="禁止设置永久有效", retry_suggestion=False),
    403153: ErrorCodeDetail(http_status=403, errmsg_doc="Exceed the maximum download times", remarks="无法执行下载操作，您的下载次数超出限制", retry_suggestion=False),
    403154: ErrorCodeDetail(http_status=403, errmsg_doc="This quota is less than actual used space.", remarks="当前配额空间小于已用空间", retry_suggestion=False),
    403155: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to edit File Outbox", remarks="无法编辑\"\{发送文件箱\}\"", retry_suggestion=False),
    403156: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share My Documents and cannot set permission.", remarks="您没有共享个人文档权限，无法进行权限配置。", retry_suggestion=False),
    403157: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share Group Documents and cannot set permission.", remarks="您没有共享群组文档权限，无法进行权限配置。", retry_suggestion=False),
    403158: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share My Documents and cannot invite others.", remarks="您没有共享个人文档权限，无法开启共享邀请。", retry_suggestion=False),
    403159: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share Group Documents and cannot invite others.", remarks="您没有共享群组文档权限，无法开启共享邀请。", retry_suggestion=False),
    403160: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share My Documents and cannot enable public share.", remarks="您没有共享个人文档权限，无法开启发现共享。", retry_suggestion=False),
    403161: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share Group Documents and cannot enable public share.", remarks="您没有共享群组文档权限，无法开启发现共享。", retry_suggestion=False),
    403162: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share My Documents and cannot enable external link", remarks="您没有共享个人文档权限，无法开启外链", retry_suggestion=False),
    403163: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share Group Documents and cannot enable external link", remarks="您没有共享群组文档权限，无法开启外链", retry_suggestion=False),
    403164: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share My Documents and cannot modify external link", remarks="您没有共享个人文档权限，无法修改外链", retry_suggestion=False),
    403165: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share Group Documents and cannot modify external link", remarks="您没有共享群组文档权限，无法修改外链", retry_suggestion=False),
    403166: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share My Documents and cannot invite others.", remarks="您没有共享个人文档权限，无法修改共享邀请", retry_suggestion=False),
    403167: ErrorCodeDetail(http_status=403, errmsg_doc="You do not have permission to share Group Documents and cannot invite others.", remarks="您没有共享群组文档权限，无法修改共享邀请", retry_suggestion=False),
    403168: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to copy file/folder to watermark library.", remarks="无法将文件/文件夹复制出水印文档库(旧水印方案为：复制到)", retry_suggestion=False),
    403169: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to move file/folder to watermark library.", remarks="无法将文件/文件夹移动出水印文档库(旧水印方案为：复制到)", retry_suggestion=False),
    403170: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to make watermark.", remarks="添加下载水印失败", retry_suggestion=False),
    403171: ErrorCodeDetail(http_status=403, errmsg_doc="user freezed.", remarks="用户被冻结", retry_suggestion=False),
    403172: ErrorCodeDetail(http_status=403, errmsg_doc="doc creator freezed.", remarks="文档创建者被冻结", retry_suggestion=False),
    403173: ErrorCodeDetail(http_status=403, errmsg_doc="cannot set owner", remarks="禁止设置所有者", retry_suggestion=False),
    403174: ErrorCodeDetail(http_status=403, errmsg_doc="low version", remarks="客户端版本过低", retry_suggestion=False),
    403175: ErrorCodeDetail(http_status=403, errmsg_doc="destination file is locked", remarks="目的文件被锁定", retry_suggestion=False),
    403176: ErrorCodeDetail(http_status=403, errmsg_doc="source file is locked", remarks="源文件被锁定", retry_suggestion=False),
    403177: ErrorCodeDetail(http_status=403, errmsg_doc="Not owner", remarks="不是所有者", retry_suggestion=False),
    403178: ErrorCodeDetail(http_status=403, errmsg_doc="File of this format is unsupported for watermark", remarks="此格式文件不支持水印", retry_suggestion=False),
    403179: ErrorCodeDetail(http_status=403, errmsg_doc="user have not completed real-name authentication.", remarks="用户未实名认证", retry_suggestion=False),
    403180: ErrorCodeDetail(http_status=403, errmsg_doc="doc creater have not completed real-name authentication.", remarks="文档创建者未实名认证", retry_suggestion=False),
    403181: ErrorCodeDetail(http_status=403, errmsg_doc="File suffix type limited", remarks="文档类型被限制上传", retry_suggestion=False),
    403182: ErrorCodeDetail(http_status=403, errmsg_doc="Cant rename, file suffix type limited", remarks="重命名失败，文件类型已被管理员禁用", retry_suggestion=False),
    403183: ErrorCodeDetail(http_status=403, errmsg_doc="cannot batchdownload watermark docs.", remarks="水印范围内的文件不支持批量下载", retry_suggestion=False),
    403184: ErrorCodeDetail(http_status=403, errmsg_doc="Permission modification to the creator of the Group Document is not allowed", remarks="不允许对群组文档的创建者配置权限", retry_suggestion=False),
    403185: ErrorCodeDetail(http_status=403, errmsg_doc="Invalid email address", remarks="邮箱地址非法", retry_suggestion=False),
    403186: ErrorCodeDetail(http_status=403, errmsg_doc="duplicated email address", remarks="邮箱地址重复", retry_suggestion=False),
    403187: ErrorCodeDetail(http_status=403, errmsg_doc="Document process invisible", remarks="流程不可见", retry_suggestion=False),
    403188: ErrorCodeDetail(http_status=403, errmsg_doc="The document sending and receiving task in this directory already exists", remarks="该目录下文档收发任务已存在", retry_suggestion=False),
    403189: ErrorCodeDetail(http_status=403, errmsg_doc="The task of sending and receiving documents in this directory does not exist", remarks="该目录下文档收发任务不存在", retry_suggestion=False),
    403190: ErrorCodeDetail(http_status=403, errmsg_doc="There is a task recipient and cannot delete the task", remarks="存在任务接收者，无法删除任务", retry_suggestion=False),
    403191: ErrorCodeDetail(http_status=403, errmsg_doc="Invalid group name", remarks="无效的组名", retry_suggestion=False),
    403192: ErrorCodeDetail(http_status=403, errmsg_doc="The contact group has already existed", remarks="联系人组已存在", retry_suggestion=False),
    403193: ErrorCodeDetail(http_status=403, errmsg_doc="Inability to operate a temporary group", remarks="不能操作临时组", retry_suggestion=False),
    403194: ErrorCodeDetail(http_status=403, errmsg_doc="The contact group does not exist", remarks="联系人组不存在", retry_suggestion=False),
    403195: ErrorCodeDetail(http_status=403, errmsg_doc="Contacts do not exist", remarks="联系人不存在", retry_suggestion=False),
    403196: ErrorCodeDetail(http_status=403, errmsg_doc="Failed to get device information", remarks="获取设备信息失败", retry_suggestion=False),
    403197: ErrorCodeDetail(http_status=403, errmsg_doc="User self-registration failed", remarks="用户自注册失败", retry_suggestion=False),
    403198: ErrorCodeDetail(http_status=403, errmsg_doc="User has registered", remarks="用户已注册", retry_suggestion=False),
    403199: ErrorCodeDetail(http_status=403, errmsg_doc="Recvice area not exist", remarks="接收区不存在", retry_suggestion=False),
    403200: ErrorCodeDetail(http_status=403, errmsg_doc="Ttarget location is illegal", remarks="目的位置不合法", retry_suggestion=False),
    403201: ErrorCodeDetail(http_status=403, errmsg_doc="Exit password disabled", remarks="退出口令未开启", retry_suggestion=False),
    403202: ErrorCodeDetail(http_status=403, errmsg_doc="Exit password error", remarks="退出口令错误", retry_suggestion=False),
    403203: ErrorCodeDetail(http_status=403, errmsg_doc="File version conflict", remarks="客户端编辑的文件版本与服务端文件的最新版本不一致", retry_suggestion=False),
    403204: ErrorCodeDetail(http_status=403, errmsg_doc="The owning CID library has not been deleted", remarks="所属CID文档库未被删除", retry_suggestion=False),
    403205: ErrorCodeDetail(http_status=403, errmsg_doc="file not scan virus", remarks="版本未查毒", retry_suggestion=True),
    404001: ErrorCodeDetail(http_status=404, errmsg_doc="group not exists", remarks="群组不存在", retry_suggestion=False),
    404002: ErrorCodeDetail(http_status=404, errmsg_doc="department not exist", remarks="获取子部门或子用户信息时，指定部门不存在", retry_suggestion=False),
    404004: ErrorCodeDetail(http_status=404, errmsg_doc="user not exists when get the information", remarks="获取用户信息时，用户不存在", retry_suggestion=False),
    404005: ErrorCodeDetail(http_status=404, errmsg_doc="entrance doc info record not exists", remarks="入口文档记录不存在", retry_suggestion=False),
    404006: ErrorCodeDetail(http_status=404, errmsg_doc="object requested not exists", remarks="请求的文件或目录不存在", retry_suggestion=False),
    404008: ErrorCodeDetail(http_status=404, errmsg_doc="out link not exists", remarks="不存在的外链", retry_suggestion=False),
    404010: ErrorCodeDetail(http_status=404, errmsg_doc="no completed version", remarks="没有完整的版本", retry_suggestion=False),
    404011: ErrorCodeDetail(http_status=404, errmsg_doc="data block metadata not exists or damaged", remarks="数据块信息不完整", retry_suggestion=False),
    404012: ErrorCodeDetail(http_status=404, errmsg_doc="encryption key does not exist", remarks="文档库加密密钥不存在", retry_suggestion=False),
    404013: ErrorCodeDetail(http_status=404, errmsg_doc="destination object requested not exists", remarks="请求的目标端文件或目录不存在", retry_suggestion=False),
    404014: ErrorCodeDetail(http_status=404, errmsg_doc="Failed to get open storage service information", remarks="获取开放存储服务信息失败", retry_suggestion=False),
    404015: ErrorCodeDetail(http_status=404, errmsg_doc="Failed to get uploadid", remarks="获取uploadid失败", retry_suggestion=False),
    404016: ErrorCodeDetail(http_status=404, errmsg_doc="Client Update Package not upload", remarks="客户端升级包未上传", retry_suggestion=False),
    404017: ErrorCodeDetail(http_status=404, errmsg_doc="Failed to get site information", remarks="获取站点信息失败", retry_suggestion=False),
    404018: ErrorCodeDetail(http_status=404, errmsg_doc="Archive doc not support sort by type", remarks="归档库不支持类型排序", retry_suggestion=False),
    404019: ErrorCodeDetail(http_status=404, errmsg_doc="User does not have doc", remarks="用户没有个人文档", retry_suggestion=False),
    404020: ErrorCodeDetail(http_status=404, errmsg_doc="Smtp recipient mail illegal", remarks="Smtp服务器收件人邮箱地址不合法", retry_suggestion=False),
    404021: ErrorCodeDetail(http_status=404, errmsg_doc="Smtp server not set", remarks="Smtp服务器未设置", retry_suggestion=False),
    404022: ErrorCodeDetail(http_status=404, errmsg_doc="Smtp send failed", remarks="Smtp邮件发送失败", retry_suggestion=True),
    404023: ErrorCodeDetail(http_status=404, errmsg_doc="Smtp server not available", remarks="Smtp服务器不可达", retry_suggestion=True),
    404027: ErrorCodeDetail(http_status=404, errmsg_doc="Insufficient system resources to access", remarks="系统资源不足，无法访问", retry_suggestion=False),
    405001: ErrorCodeDetail(http_status=405, errmsg_doc="method not allowed for this uri", remarks="HTTP方法错误", retry_suggestion=False),
    500001: ErrorCodeDetail(http_status=500, errmsg_doc="internal error", remarks="内部错误", retry_suggestion=False),
    500002: ErrorCodeDetail(http_status=500, errmsg_doc="preview file or image failed", remarks="预览文件或缩略图失败", retry_suggestion=False),
    500006: ErrorCodeDetail(http_status=500, errmsg_doc="program and data not compatible", remarks="程序与当前数据不兼容", retry_suggestion=False),
    500007: ErrorCodeDetail(http_status=500, errmsg_doc="version of the sever not support client", remarks="服务器版本不支持该客户端", retry_suggestion=False),
    500008: ErrorCodeDetail(http_status=500, errmsg_doc="could not connect index server", remarks="全文检索，链接索引服务器失败", retry_suggestion=False),
    500009: ErrorCodeDetail(http_status=500, errmsg_doc="index server internal error", remarks="全文检索，索引服务器内部错误", retry_suggestion=False),
    500010: ErrorCodeDetail(http_status=500, errmsg_doc="request to storage server error", remarks="请求存储服务器失败", retry_suggestion=False),
    500011: ErrorCodeDetail(http_status=500, errmsg_doc="Search service is not installed", remarks="全文检索未安装", retry_suggestion=False),
    500012: ErrorCodeDetail(http_status=500, errmsg_doc="Please confirm multi-site configuration before restoring the data", remarks="恢复数据前，请确认多站点配置信息", retry_suggestion=False),
    501001: ErrorCodeDetail(http_status=501, errmsg_doc="not implemented", remarks="不支持除POST外的其它方法", retry_suggestion=False),
    501002: ErrorCodeDetail(http_status=501, errmsg_doc="Api is not implemented", remarks="请求API错误，调用了不支持的API。", retry_suggestion=False),
    503001: ErrorCodeDetail(http_status=503, errmsg_doc="server is busy, please try again later", remarks="服务器繁忙，请稍后重试", retry_suggestion=True),
    503002: ErrorCodeDetail(http_status=503, errmsg_doc="preview failed，server is doing the conversion", remarks="文档正在转换", retry_suggestion=True),
    503003: ErrorCodeDetail(http_status=503, errmsg_doc="Backuping database", remarks="正在备份数据库", retry_suggestion=True),
    503004: ErrorCodeDetail(http_status=503, errmsg_doc="Recovering database", remarks="正在还原数据库", retry_suggestion=True),
    503005: ErrorCodeDetail(http_status=503, errmsg_doc="Making watermark, please try again later...", remarks="正在制作水印，请稍后...", retry_suggestion=True),
    503006: ErrorCodeDetail(http_status=503, errmsg_doc="Begin to add watermark", remarks="开始添加下载水印", retry_suggestion=True),
}

from typing import Union, Any

class AnyshareApiException(Exception):
    """Custom exception for AnyshareClient operations."""
    def __init__(self,
                 message: str,
                 location: Optional[str] = None,
                 error_code: Optional[Union[str, int]] = None,
                 details: Optional[Any] = None,
                 original_exception: Optional[BaseException] = None):
        full_message = f"Anyshare API Error: {message}"
        if location:
            full_message += f" (Location: {location})"
        if error_code is not None:
            full_message += f" (Code: {error_code})"
        if details:
            full_message += f" (Details: {str(details)})"

        super().__init__(full_message)
        self.message = message
        self.location = location
        self.error_code = error_code
        self.details = details
        self.original_exception = original_exception

    def __str__(self):
        base_str = super().__str__()
        if self.original_exception:
            # Ensure original_exception string representation is not too verbose or problematic
            original_exc_str = str(self.original_exception)
            # Basic sanitization/truncation if necessary, e.g. limit length
            max_len = 200
            if len(original_exc_str) > max_len:
                original_exc_str = original_exc_str[:max_len] + "..."
            return f"{base_str} | Caused by: {type(self.original_exception).__name__}: {original_exc_str}"
        return base_str
