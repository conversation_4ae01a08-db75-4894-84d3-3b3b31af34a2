from pydantic import BaseModel, Field
from typing import Optional, List

class AutolockLockReq(BaseModel):
    """锁定文件的请求体"""
    docid: str = Field(description="文件id")
    force: Optional[bool] = Field(default=None, description="是否强制获取锁，默认为false")
    expiretime: Optional[int] = Field(default=None, description="文件锁有效期限（微秒时间戳），默认为-1（永久有效）")

class AutolockTrylockReq(BaseModel):
    """尝试锁定文件的请求体"""
    docid: str = Field(description="文件id")
    force: Optional[bool] = Field(default=None, description="是否强制获取锁，默认为false")
    expiretime: Optional[int] = Field(default=None, description="文件锁有效期限（微秒时间戳），默认为-1（永久有效）")

class AutolockTrylockRes(BaseModel):
    """尝试锁定文件的响应体"""
    issucceed: bool = Field(description="锁定文件成功则返回true，文件已被其他用户锁定则返回false")
    lockerid: Optional[str] = Field(default=None, description="文件已被其他用户时，锁定者的用户id")
    lockeraccount: Optional[str] = Field(default=None, description="文件已被其他用户时，锁定者用户名")
    lockername: Optional[str] = Field(default=None, description="文件已被其他用户时，锁定者显示名")

class AutolockRefreshReq(BaseModel):
    """刷新文件锁的请求体"""
    lockinfos: List[str] = Field(description="待刷新的文件锁信息 (docid 列表)")

class AutolockRefreshResLockinfo(BaseModel):
    """刷新文件锁结果中的单个文件锁信息"""
    docid: str = Field(description="文件id")
    state: int = Field(description="state等于0时表示刷新成功, state等于1表示发生异常")
    errmsg: Optional[str] = Field(default=None, description="异常内容，state等于1时会填充该字段")

class AutolockRefreshRes(BaseModel):
    """刷新文件锁的响应体"""
    lockinfos: List[AutolockRefreshResLockinfo] = Field(description="文件锁刷新后的状态")

class AutolockUnlockReq(BaseModel):
    """解锁文件的请求体"""
    docid: str = Field(description="文件id")

# 注意：/autolock/unlock 的成功响应没有主体，通常返回 HTTP 200 OK

class AutolockGetlockinfoReq(BaseModel):
    """获取文件锁信息的请求体"""
    docid: str = Field(description="文件id")

class AutolockGetlockinfoRes(BaseModel):
    """获取文件锁信息的响应体"""
    islocked: bool = Field(description="文件锁定则返回true，文件未锁定则返回false")
    lockerid: Optional[str] = Field(default=None, description="文件已被其他用户时，锁定者的用户id")
    lockeraccount: Optional[str] = Field(default=None, description="文件已被其他用户时，锁定者用户名")
    lockername: Optional[str] = Field(default=None, description="文件已被其他用户时，锁定者显示名")

class AutolockGetdirlockinfoReq(BaseModel):
    """获取文件夹锁信息的请求体"""
    docid: str = Field(description="文件夹id")

class AutolockGetdirlockinfoRes(BaseModel):
    """获取文件夹锁信息的响应体"""
    islocked: bool = Field(description="文件夹下有锁定文件则返回true，文件下没有锁定文件则返回false")

class AutolockGetlockedfileinfosReq(BaseModel):
    """获取当前用户锁信息的请求体"""
    start: Optional[int] = Field(default=None, description="开始位置，默认为0")
    limit: Optional[int] = Field(default=None, description="分页条数，默认为-1，返回start后面的所有记录")
    name: Optional[str] = Field(default=None, description="按照文件名查找，默认为空，不进行过滤")
    locker: Optional[str] = Field(default=None, description="按照锁定者查找，默认为空，不进行过滤")

class AutolockGetlockedfileinfosResDocinfo(BaseModel):
    """当前用户锁信息中的单个文档信息"""
    locktime: int = Field(description="文件锁创建时间（微秒时间戳）")
    docid: str = Field(description="文件id")
    lockeraccount: str = Field(description="锁定者用户名")
    lockerid: str = Field(description="锁定者id")
    lockername: str = Field(description="锁定者显示名")
    path: str = Field(description="文件路径")

class AutolockGetlockedfileinfosRes(BaseModel):
    """获取当前用户锁信息的响应体"""
    docinfos: List[AutolockGetlockedfileinfosResDocinfo] = Field(description="包含多个文档锁信息的列表")

# CommonError schema is usually handled by a base API client or globally.
# We can define it here for completeness if needed, or assume BaseApi handles it.
# class Error(BaseModel):
#     errcode: int = Field(description="错误码")
#     errmsg: str = Field(description="错误信息")
#     causemsg: str = Field(description="详细错误信息") 