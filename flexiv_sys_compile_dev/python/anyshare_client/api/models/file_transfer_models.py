# Models for File Transfer API
from typing import List, Optional, Dict, Any, Tuple
from pydantic import BaseModel, Field

class FileOsdownloadReq(BaseModel):
    docid: str = Field(description="文档id")
    rev: Optional[str] = Field(None, description="文件版本号，为空默认下载最新版本")
    authtype: Optional[str] = Field(None, description="默认为空，在header中包含鉴权，否则在url中包含鉴权")
    reqhost: Optional[str] = Field(None, description="从存储服务器下载数据时的请求地址")
    usehttps: Optional[bool] = Field(True, description="是否使用https下载，默认为true；使用亚马逊AWS S3时，参数usehttps的设置无效，统一使用https")
    savename: Optional[str] = Field(None, description="使用QUERY_STRING方式下载时（浏览器），可以设置要保存的文件名")

class FileOsdownloadRes(BaseModel):
    need_watermark: bool = Field(description="是否是下载水印文件")
    authrequest: List[str] = Field(description="- authrequest[0]：请求方法\\n- authrequest[1]：资源URL\\n- authrequest[2]（如果存在）及以后项为http请求的headers，如自定义date，Content-type等，格式为“key: value”")
    client_mtime: int = Field(description="由客户端设置的文件本地修改时间\\n下载第0块时返回，若未设置，返回modified的值")
    editor: str = Field(description="编辑者名称， UTF8编码")
    modified: int = Field(description="上传时间， UTC时间，此为上传版本时的服务器时间")
    name: str = Field(description="文件的当前名称，UTF8编码")
    rev: str = Field(description="文件版本号")
    size: int = Field(description="当前下载版本的总大小")

class FileOsoptionRes(BaseModel):
    partminsize: int = Field(description="大文件分片上传，除最后一块外，其它分片的最小值；当partminsize小于等于4M时，可按照4M分块；当partminsize大于4M时，分块的大小必须大于等于partminsize。")
    partmaxsize: int = Field(description="大文件分片上传，除最后一块外，其它分片的最大值")
    partmaxnum: int = Field(description="大文件分片上传，最大的分片数量")

class FileOsbeginuploadReq(BaseModel):
    docid: str = Field(description="gns（全局名字空间）路径，创建或者列举协议返回\\n- 创建文件时， gns为待创建文件的父目录gns；\\n- 编辑文件时（文件生成新版本），gns为所编辑文件的gns")
    length: int = Field(description="整个文件的长度，支持最大5G")
    name: Optional[str] = Field(None, description="文件名称，UTF8编码\\n- 创建文件时， name为文件名；\\n- 编辑文件时（文件生成新版本），参数为空\\n")
    client_mtime: Optional[int] = Field(None, description="由客户端设置的文件本地修改时间\\n创建新版本（rev为空或name为为空）时，写入版本")
    ondup: Optional[int] = Field(None, description="当name不为空时生效\\n- 1:检查是否重命名，重名则抛异常\\n- 2:如果重名冲突，自动重名\\n- 3:如果重名冲突，自动覆盖\\n")
    reqmethod: Optional[str] = Field("PUT", description="向存储服务器上传数据时的请求方法。\\n默认为“PUT”；参数值“POST”表示使用POST表单的方式上传\\n")
    reqhost: Optional[str] = Field(None, description="向存储服务器上传数据时的请求地址")
    usehttps: Optional[bool] = Field(True, description="上传是否使用https，默认为true，只对一体机自有对象存储EOSS生效")
    csflevel: Optional[int] = Field(0, description="文件密级预检查，要设置密级必须在osendupload中设置\\n- 0：默认值，不检查密级\\n- 5~15：正常密级\\n- 0x7FFF：空密级\\n")
    editedrev: Optional[str] = Field(None, description="表示编辑文件时，基于文件的哪个版本（版本ID）进行编辑，默认为空。\\n- 如果为空，不检查是否基于文件的最新版本进行编辑；\\n- 如果不为空，若所编辑的版本ID不是服务器上文件的最新版本，则上传报错。错误码403203，“所编辑文件的版本与服务端文件最新版本不一致”\\n")

class FileOsbeginuploadRes(BaseModel):
    authrequest: List[str] = Field(description="- authrequest[0]：请求方法\\n- authrequest[1]: 待上传的资源URL\\n- authrequest[2]及以后项：请求头，格式为“key: value”；若请求时reqmethod的值为“POST”，则为POST表单域的项，格式为“key: value”\\n")
    docid: str = Field(description="文件的gns路径")
    name: str = Field(description="文件名称，UTF8编码，创建版本时返回")
    rev: str = Field(description="文件版本号")

class FileOsenduploadReq(BaseModel):
    docid: str = Field(description="文件gns路径（创建协议返回）")
    rev: str = Field(description="文件版本号")
    crc32: Optional[str] = Field(None, description="文件的CRC32校验码")
    md5: Optional[str] = Field(None, description="文件MD5值")
    slice_md5: Optional[str] = Field(None, description="文件的slice_md5")
    csflevel: Optional[int] = Field(0, description="文件密级，仅上传定密时需要设置\\n- 0：默认值，创建文件时文件密级设为创建者密级，覆盖版本时不改变密级\\n- 5~15：正常密级\\n- 0x7FFF：空密级\\n")
    editedrev: Optional[str] = Field(None, description="表示编辑文件时，基于文件的哪个版本（版本ID）进行编辑，默认为空。\\n- 如果为空，不检查是否基于文件的最新版本进行编辑；\\n- 如果不为空，若所编辑的版本ID不是服务器上文件的最新版本，则上传报错。错误码403203，“所编辑文件的版本与服务端文件最新版本不一致”；\\n")

class FileOsenduploadRes(BaseModel):
    editor: str = Field(description="编辑者")
    modified: int = Field(description="上传时间，UTC时间，此为上传版本完成时的服务器时间")

class FileOsinitmultiuploadReq(BaseModel):
    docid: str = Field(description="gns（全局名字空间）路径，创建或者列举协议返回\\n- 创建文件时， gns为待创建文件的父目录gns；\\n- 编辑文件时（文件生成新版本），gns为所编辑文件的gns；\\n")
    length: int = Field(description="整个文件的长度")
    name: Optional[str] = Field(None, description="文件名称，UTF8编码\\n- 创建文件时， name为文件名；\\n- 编辑文件时（文件生成新版本），参数为空；\\n")
    client_mtime: Optional[int] = Field(None, description="由客户端设置的文件本地修改时间\\n\\n创建新版本（rev为空或name为为空）时，写入版本\\n")
    ondup: Optional[int] = Field(None, description="当name不为空时生效\\n- 1:检查是否重命名，重名则抛异常\\n- 2:如果重名冲突，自动重名\\n- 3:如果重名冲突，自动覆盖\\n")
    csflevel: Optional[int] = Field(0, description="文件密级预检查，要设置密级必须在osendupload中设置\\n- 0：默认值，不检查密级\\n- 5~15：正常密级\\n- 0x7FFF：空密级\\n")
    editedrev: Optional[str] = Field(None, description="表示编辑文件时，基于文件的哪个版本（版本ID）进行编辑，默认为空。\\n- 如果为空，不检查是否基于文件的最新版本进行编辑；\\n- 如果不为空，若所编辑的版本ID不是服务器上文件的最新版本，则上传报错。错误码403203，“所编辑文件的版本与服务端文件最新版本不一致”；\\n")

class FileOsinitmultiuploadRes(BaseModel):
    docid: str = Field(description="文件的gns路径")
    name: str = Field(description="文件名称，UTF8编码，创建版本时返回")
    rev: str = Field(description="文件版本号")
    uploadid: str = Field(description="标识本次Multipart Upload事件")

class FileOsuploadpartReq(BaseModel):
    docid: str = Field(description="文件的gns路径（创建协议返回）")
    rev: str = Field(description="文件版本号")
    uploadid: str = Field(description="Multiupload事件Id")
    parts: str = Field(description="需要鉴权的数据块号，支持两种格式的组合，组合时以逗号分隔：\\n- 以“-”连接的数据块区间\\n- 单独的数据块号\\n- 如：1-100,2,3,4,23-120,130,288\\n- 数据块号最小为1，最大为10000。\\n- 根据parts参数的请求形式，可以批量申请上传数据的请求，减少上传文件时的请求次数。\\n")
    reqhost: Optional[str] = Field(None, description="向存储服务器上传数据时的请求地址")
    usehttps: Optional[bool] = Field(True, description="上传是否使用https，默认为true")

class FileOsuploadpartRes(BaseModel):
    authrequests: Dict[str, List[str]] = Field(description="分块上传信息map，键为数据块号，值为上传信息authrequest\\n- authrequest[0]：请求方法\\n- authrequest[1]: 待上传的资源URL\\n- authrequest[2]及以后项：请求头，格式为“key: value”")

class FileOscompleteuploadReq(BaseModel):
    docid: str = Field(description="文件的gns路径（创建协议返回）")
    rev: str = Field(description="文件版本号")
    uploadid: str = Field(description="Multiupload事件Id")
    partinfo: Dict[str, List[Any]] = Field(description="分块信息map，键为数据分块号, 值为一个列表，第一个元素是Etag(str)，第二个元素是size(int)。 例如: { \"1\": [ \"etag_value\", 4194304 ] }")
    reqhost: Optional[str] = Field(None, description="向存储服务器上传数据时的请求地址")
    usehttps: Optional[bool] = Field(True, description="上传是否使用https，默认为true")

class FileOscompleteuploadRes(BaseModel):
    # OpenAPI schema for this response (200, multipart/form-data) is `type: \"string\"`.
    # This model field will store the raw string response.
    # The API method's documentation should guide the user on how to parse
    # the multipart content, including the JSON part (authrequest) and XML part.
    raw_response: str = Field(description="原始的 multipart/form-data 响应内容。包含JSON和XML部分，需要调用者自行解析。")

class FileOsuploadrefreshReq(BaseModel):
    docid: str = Field(description="文件gns路径（创建协议返回）")
    rev: str = Field(description="文件版本号")
    length: Optional[int] = Field(-1, description="默认为-1；\\n\\n不为-1时，更新版本的length为参数值；\\n")
    multiupload: Optional[bool] = Field(False, description="- 默认为false\\n- 参数为true，返回大文件分片上传需要的uploadid\\n- 如果multiupload为false，length支持最大5G")
    reqmethod: Optional[str] = Field("PUT", description="采用单文件上传方式向存储服务器上传数据时的请求方法。\\n\\n默认为“PUT”；参数值“POST”表示使用POST表单的方式上传\\n")
    reqhost: Optional[str] = Field(None, description="向存储服务器上传数据时的请求地址")
    usehttps: Optional[bool] = Field(True, description="- 上传是否使用https，默认为true\\n- 该参数只对一体机自有对象存储EOSS生效")

class FileOsuploadrefreshRes(BaseModel):
    uploadid: Optional[str] = Field(None, description="标识本次Multipart Upload事件")
    authrequest: Optional[List[str]] = Field(None, description="- authrequest[0]：请求方法\\n- authrequest[1]: 待上传的资源URL\\n- authrequest[2]及以后项：请求头；若请求时reqmethod的值为“POST”，则为POST表单域的项，格式为“key: value”")

class FileBatchdownloadReq(BaseModel):
    name: str = Field(..., description="zip压缩包名称")
    reqhost: str = Field(..., description="从存储服务器下载数据时的请求地址")
    usehttps: Optional[bool] = Field(False, description="上传是否使用https，默认为False")
    files: Optional[List[str]] = Field(None, description="文件GNS数组")
    dirs: Optional[List[str]] = Field(None, description="文件夹GNS数组")

class FileBatchdownloadRes(BaseModel):
    method: str = Field(description="url请求方法")
    url: str = Field(description="文件批量下载地址")

# Ensure all model names are added to __all__ in models/__init__.py 