from pydantic import BaseModel, Field
from typing import Optional, List, Any

class DirListResDir(BaseModel):
    docid: str = Field(description="目录/文件docid")
    name: str = Field(description="目录/文件名称")
    rev: str = Field(description="目录/文件版本号")
    size: int = Field(description="目录/文件大小")
    create_time: int = Field(description="创建时间")
    creator: str = Field(description="创建者")
    modified: int = Field(description="修改时间")
    editor: str = Field(description="编辑者")
    csflevel: Optional[int] = Field(default=None, description="CSF级别")
    client_mtime: Optional[int] = Field(default=None, description="客户端修改时间")
    attr: Optional[int] = Field(default=None, description="属性")
    duedate: Optional[int] = Field(default=None, description="截止日期")

class DirListResFile(BaseModel):
    docid: str = Field(description="文件docid")
    name: str = Field(description="文件名称")
    rev: str = Field(description="文件版本号")
    size: int = Field(description="文件大小")
    create_time: int = Field(description="创建时间")
    creator: str = Field(description="创建者")
    modified: int = Field(description="修改时间")
    editor: str = Field(description="编辑者")
    csflevel: Optional[int] = Field(default=None, description="CSF级别")
    client_mtime: Optional[int] = Field(default=None, description="客户端修改时间")
    attr: Optional[int] = Field(default=None, description="属性")
    duedate: Optional[int] = Field(default=None, description="截止日期")

class DirSetcsflevelRes(BaseModel):
    result: int = Field(description="操作结果")

class DirListReq(BaseModel):
    docid: str = Field(description="目录docid")
    by: Optional[str] = Field(default=None, description="排序字段")
    sort: Optional[str] = Field(default=None, description="排序方式")
    attr: Optional[bool] = Field(default=None, description="是否返回属性")

class DirRenameReq(BaseModel):
    docid: str = Field(description="目录/文件docid")
    name: str = Field(description="新的名称")
    ondup: Optional[int] = Field(default=None, description="重名处理方式")

class DirIsfileoutboxReq(BaseModel):
    docid: str = Field(description="文件docid")

class DirAttributeReq(BaseModel):
    docid: str = Field(description="目录/文件docid")

class DirDeletetagReq(BaseModel):
    docid: str = Field(description="目录/文件docid")
    tag: str = Field(description="标签")

class DirCopyReq(BaseModel):
    docid: str = Field(description="源目录/文件docid")
    destparent: str = Field(description="目标父目录docid")
    ondup: Optional[int] = Field(default=None, description="重名处理方式")

class DirGetsuggestnameReq(BaseModel):
    docid: str = Field(description="目录/文件docid")
    name: str = Field(description="原始名称")

class DirIsfileoutboxRes(BaseModel):
    isfileoutbox: bool = Field(description="是否为外发文件")

class DirAddtagsReq(BaseModel):
    docid: str = Field(description="目录/文件docid")
    tags: List[str] = Field(description="标签列表")

class DirCheckwatermarkRes(BaseModel):
    watermarktype: int = Field(description="水印类型") # Renamed from "watermarktype " due to trailing space in JSON

class DirAddtagReq(BaseModel):
    docid: str = Field(description="目录/文件docid")
    tag: str = Field(description="标签")

class DirMoveRes(BaseModel):
    docid: str = Field(description="移动后的docid")
    isdirexist: bool = Field(description="目录是否存在")
    name: Optional[str] = Field(default=None, description="名称")

class DirSetcsflevelReq(BaseModel):
    csflevel: int = Field(description="CSF级别")
    docid: str = Field(description="目录/文件docid")

class DirCopyprogressRes(BaseModel):
    success: bool = Field(description="是否成功")
    filecount: Optional[int] = Field(default=None, description="文件数量") # Original JSON shows string for some numerics, assuming int
    dircount: Optional[int] = Field(default=None, description="目录数量")
    filecopied: Optional[int] = Field(default=None, description="已复制文件数量")
    dircopied: Optional[int] = Field(default=None, description="已复制目录数量")
    size: Optional[int] = Field(default=None, description="总大小")
    sizecopied: Optional[int] = Field(default=None, description="已复制大小")
    filecopying: Optional[str] = Field(default=None, description="正在复制的文件")
    source: Optional[str] = Field(default=None, description="源路径")
    destination: Optional[str] = Field(default=None, description="目标路径")

class DirCopyprogressReq(BaseModel):
    id: str = Field(description="任务id")

class DirCreateReq(BaseModel):
    docid: str = Field(description="父目录docid")
    name: str = Field(description="目录名称")
    ondup: Optional[int] = Field(default=None, description="重名处理方式")

class DirCheckwatermarkReq(BaseModel):
    docid: str = Field(description="目录/文件docid")

class DirAttributeRes(BaseModel):
    create_time: int = Field(description="创建时间")
    creator: str = Field(description="创建者")
    modified: int = Field(description="修改时间")
    name: str = Field(description="名称")
    tags: List[str] = Field(description="标签列表")
    duedate: Optional[int] = Field(default=None, description="截止日期") # Added to handle unexpected field from server

class DirCreatemultileveldirReq(BaseModel):
    docid: str = Field(description="父目录docid")
    path: str = Field(description="多级目录路径")

class DirDeleteRes(BaseModel):
    isdirexist: bool = Field(description="目录是否存在")

class DirCreatemultileveldirRes(BaseModel):
    docid: str = Field(description="创建的目录docid")
    rev: str = Field(description="版本号")
    modified: int = Field(description="修改时间")

class DirListRes(BaseModel):
    dirs: List[DirListResDir] = Field(description="目录列表")
    files: List[DirListResFile] = Field(description="文件列表")

class DirMoveReq(BaseModel):
    docid: str = Field(description="源目录/文件docid")
    destparent: str = Field(description="目标父目录docid")
    ondup: Optional[int] = Field(default=None, description="重名处理方式")

class DirAddtagsRes(BaseModel):
    tag_max_num: int = Field(description="标签最大数量")
    unsettagnum: int = Field(description="未设置标签数量")
    unsettags: List[str] = Field(description="未设置标签列表")

class DirCreateRes(BaseModel):
    docid: str = Field(description="创建的目录docid")
    rev: str = Field(description="版本号")
    modified: int = Field(description="修改时间")
    create_time: int = Field(description="创建时间")
    creator: str = Field(description="创建者")
    editor: str = Field(description="编辑者")
    name: Optional[str] = Field(default=None, description="名称")

class DirRenameRes(BaseModel):
    name: Optional[str] = Field(default=None, description="新的名称")

class DirGetsuggestnameRes(BaseModel):
    name: str = Field(description="建议名称")

class DirCopyRes(BaseModel):
    docid: str = Field(description="复制后的docid")
    id: str = Field(description="任务id") # task id
    name: Optional[str] = Field(default=None, description="名称")

class DirDeleteReq(BaseModel):
    docid: str = Field(description="要删除的目录/文件docid")

class DirSizeReq(BaseModel):
    docid: str = Field(description="目录docid")
    onlyrecycle: Optional[bool] = Field(default=None, description="是否只计算回收站大小")

class DirSizeRes(BaseModel):
    dirnum: int = Field(description="目录数量")
    filenum: int = Field(description="文件数量")
    recyclesize: int = Field(description="回收站大小")
    totalsize: int = Field(description="总大小")

# Common Error model, if needed here, or assumed to be handled by BaseApi/common_models
# @dataclass
# class Error:
#     errcode: int
#     errmsg: str
#     causemsg: str
