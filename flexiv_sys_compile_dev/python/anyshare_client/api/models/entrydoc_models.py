# Placeholder for auth related data models 
from typing import List, Optional
from pydantic import BaseModel, Field

class Error(BaseModel):
    errcode: int = Field(description="错误码")
    errmsg: str = Field(description="错误描述，与错误码对应")
    causemsg: str = Field(description="错误原因，底层错误信息，仅用于研发排错，客户端不应使用此字段内容")

class Entrydoc2GetReq(BaseModel):
    doctype: int = Field(description="文档库类型：0：所有类型 1：个人文档库 2：个人群组文档库 3：自定义文档库 4：共享个人文档库 5：归档库 6：共享个人群组文档库")

class Entrydoc2GetResDocinfoSiteinfo(BaseModel):
    id: str = Field(description="站点id")
    name: str = Field(description="站点名称")

class Entrydoc2GetResDocinfo(BaseModel):
    attr: int = Field(description="总共32位权限属性")
    create_time: int = Field(description="目录创建的服务端时间")
    creator: str = Field(description="创建者名称")
    displayorder: int = Field(description="排序权重，-1表示未设置")
    docid: str = Field(description="文档库id")
    doctype: int = Field(description="文档库类型")
    downloadwatermark: bool = Field(description="是否需要下载水印")
    duedate: int = Field(description="到期时间，-1表示未设置")
    editor: str = Field(description="修改者名称")
    modified: int = Field(description="目录修改时间/文件上传时间，UTC时间")
    name: str = Field(description="文档库名称")
    rev: str = Field(description="文档库变化标识")
    siteinfo: Entrydoc2GetResDocinfoSiteinfo = Field(description="站点信息")
    size: int = Field(description="-1表示文件夹")
    typename: str = Field(description="文档库类型名称")

class Entrydoc2GetRes(BaseModel):
    docinfos: List[Entrydoc2GetResDocinfo] = Field(description="文档库信息数组")

class Entrydoc2GetdocinfoReq(BaseModel):
    docid: str = Field(description="文档库id")

class Entrydoc2GetdocinfoRes(BaseModel):
    doctype: int = Field(description="文档库类型")

class Entrydoc2GetmanagedResDocinfo(BaseModel):
    docid: str = Field(description="文档库id")
    doctype: int = Field(description="文档库类型")
    name: str = Field(description="文档库名称")
    rev: str = Field(description="文件库变化标识")
    size: int = Field(description="-1表示文件夹")
    typename: str = Field(description="文档类型名称")

class Entrydoc2GetmanagedRes(BaseModel):
    docinfos: List[Entrydoc2GetmanagedResDocinfo] = Field(description="文档库信息数组")

class Entrydoc2GetuserquotaResQuotainfo(BaseModel):
    doctype: int = Field(description="文档库类型")
    typename: str = Field(description="文档库类型名称")
    docid: str = Field(description="文档库id")
    name: str = Field(description="文档库名称")
    quota: int = Field(description="配额")
    used: int = Field(description="已用空间")

class Entrydoc2GetuserquotaRes(BaseModel):
    quotainfos: List[Entrydoc2GetuserquotaResQuotainfo] = Field(description="表示多个配额信息")

class Entrydoc2GetdocquotaReq(BaseModel):
    docid: str = Field(description="文档库id")

class Entrydoc2GetdocquotaRes(BaseModel):
    quota: int = Field(description="配额")
    used: int = Field(description="已用空间")

# Note: The Error schema is common and might be moved to a more general models file later. 