from typing import List, Optional, Union, Dict, Any
from pydantic import BaseModel, Field, RootModel


class FilePreviewossReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")
    rev: Optional[str] = Field(None, description="版本号，为空默认获取最新版本")
    type: Optional[str] = Field(None, description="需要预览的转换文件类型\\n默认参数pdf，获取转换后pdf文件的链接\\n（参数html，获取转换后html打包文件的链接，需单独部署html转换服务器）")
    reqhost: Optional[str] = Field(None, description="从存储服务器下载数据时的请求地址")
    usehttps: Optional[bool] = Field(None, description="是否使用https下载数据，默认为true")
    watermark: Optional[bool] = Field(None, description="是否增加水印，默认为true")


class FilePreviewossRes(BaseModel):
    size: int = Field(..., description="所预览文件的大小")
    url: str = Field(..., description="下载转换后文件的url(15分钟过期)")


class FileDeleteReq(BaseModel):
    docid: str = Field(..., description="要删除文件的gns路径")


class FileRenameReq(BaseModel):
    docid: str = Field(..., description="要重命名的文件gns路径")
    name: str = Field(..., description="重命名成功后的新文件名，UTF8编码")
    ondup: Optional[int] = Field(None, description="- 1:检查是否重命名，重名则抛异常\\n- 2:如果重名冲突，自动重名")


class FileRenameRes(BaseModel):
    name: Optional[str] = Field(None, description="UTF8编码，仅当ondup为2时才返回，否则返回参数仍然为空")


class FileMoveReq(BaseModel):
    docid: str = Field(..., description="要移动的文件gns路径")
    destparent: str = Field(..., description="目标父对象的gns路径")
    ondup: Optional[int] = Field(None, description="- 1:检查是否重命名，重名则抛异常\\n- 2:默认值，如果重名冲突，自动重名\\n- 3:如果重名冲突，自动覆盖")


class FileMoveRes(BaseModel):
    docid: str = Field(..., description="返回新的gns路径")
    name: Optional[str] = Field(None, description="UTF8编码，仅当ondup为2时才返回，否则返回参数仍然为空")


class FileCopyReq(BaseModel):
    docid: str = Field(..., description="要复制的文件gns路径")
    destparent: str = Field(..., description="目标父对象的gns路径")
    ondup: Optional[int] = Field(None, description="- 1:检查是否重命名，重名则抛异常\\n- 2:默认值，如果重名冲突，自动重名\\n- 3:如果重名冲突，自动覆盖")


class FileCopyRes(BaseModel):
    docid: str = Field(..., description="返回复制后的gns路径")
    name: Optional[str] = Field(None, description="UTF8编码，仅当ondup为2时才返回，否则返回参数仍然为空")


class FileGetsuggestnameReq(BaseModel):
    docid: str = Field(..., description="父目录的gns路径")
    name: str = Field(..., description="UTF-8编码，要上传的文件名")


class FileGetsuggestnameRes(BaseModel):
    name: str = Field(..., description="UTF8编码，服务端如果不存在同名的name，则直接返回name；如果存在同名的name，则根据重名冲突策略找到不冲突的文件名")


class FileRevisionsReq(BaseModel):
    docid: str = Field(..., description="要列举版本的gns路径")


class FileRevisionsRes(BaseModel):
    rev: str = Field(..., description="版本号")
    name: str = Field(..., description="版本所对应文件的最新名称")
    editor: str = Field(..., description="版本编辑者名称")
    modified: int = Field(..., description="版本上传时间，UTC时间，此为上传版本时记录的服务器时间")
    size: int = Field(..., description="版本的大小")
    client_mtime: int = Field(..., description="由客户端设置的文件本地修改时间\\n\\n若未设置，返回modified的值")


class FileRestorerevisionReq(BaseModel):
    docid: str = Field(..., description="需要还原版本的文件gns路径")
    rev: str = Field(..., description="版本号")


class FileRestorerevisionRes(BaseModel):
    editor: str = Field(..., description="版本编辑者名称")
    modified: int = Field(..., description="UTC时间，此为还原产生新版本时的服务器时间")
    client_mtime: int = Field(..., description="如果是文件，返回由客户端设置的文件本地修改时间\\n\\n若未设置，返回modified的值")
    name: str = Field(..., description="版本所对应文件的名称")
    rev: str = Field(..., description="新的版本号")


class FileMetadataReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")
    rev: Optional[str] = Field(None, description="版本号，为空默认获取最新版本的元数据")


class FileMetadataRes(BaseModel):
    client_mtime: int = Field(..., description="由客户端设置的文件本地修改时间\\n\\n若未设置，返回modified的值")
    editor: str = Field(..., description="文件版本上传编辑者名称，UTF8编码")
    modified: int = Field(..., description="文件版本上传时间，UTC时间，此为上传版本时的服务器时间")
    name: str = Field(..., description="文件版本上传时文件名称，UTF8编码")
    rev: str = Field(..., description="文件版本ID")
    site: str = Field(..., description="文件版本的归属站点")
    size: int = Field(..., description="文件版本的大小")
    tags: List[str] = Field(..., description="版本的标签，字符串数组")
    needdownloadwatermark: bool = Field(..., description="用户下载文件对象时，是否需要下载水印")


class FileAttributeReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")


class FileAttributeRes(BaseModel):
    create_time: int = Field(..., description="返回服务器端modified的值")
    creator: str = Field(..., description="文件创建者")
    csflevel: int = Field(..., description="文件密级，5~15")
    name: str = Field(..., description="文件名，UTF8编码")
    tags: List[str] = Field(..., description="文件的标签，字符串数组")
    uniqueid: Optional[str] = Field(None, description="对于归档库文件，返回文件唯一标识")


class FileSetcsflevelReq(BaseModel):
    csflevel: int = Field(..., description="文件密级：5~15 ")
    docid: str = Field(..., description="文件gns路径（列举协议返回）")


class FileSetcsflevelRes(BaseModel):
    result: int = Field(..., description="0表示没有密级变化或者成功设置；\\n\\n1表示申请已提交")


class FileAddtagReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")
    tag: str = Field(..., description="标签名")


class FileAddtagsReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")
    tags: List[str] = Field(..., description="- 标签数组\\n- 添加的标签中，不允许包含 \\\\/:*?\\\"<>|#")


class FileAddtagsRes(BaseModel):
    tag_max_num: int = Field(..., description="允许文件拥有的标签的最大数量")
    unsettagnum: int = Field(..., description="由于标签数量限制未设置成功的标签数")
    unsettags: List[str] = Field(..., description="未设置成功的标签数组")


class FileDeletetagReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")
    tag: str = Field(..., description="标签名")


class FilePreduploadReq(BaseModel):
    length: int = Field(..., description="整个文件的长度")
    slice_md5: str = Field(..., description="- 校验段校验码\\n- 如果文件大于200KB，slice_md5为文件的前200KB的MD5值\\n- 如果文件小于等于200KB，slice_md5为整个文件的MD5值\\n- 如果是空文件，slilce为空。")


class FilePreduploadRes(BaseModel):
    match: bool = Field(..., description="是否有可能存在")


class FileDuploadReq(BaseModel):
    client_mtime: Optional[int] = Field(None, description="由客户端设置的文件本地修改时间")
    crc32: str = Field(..., description="文件的CRC32校验码，如果为空文件, 此字段为空")
    docid: str = Field(..., description="gns（全局名字空间）路径，创建或者列举协议返回\\n\\n说明：如果name不为空，gns为待创建文件的父目录gns；否则为文件的gns。")
    length: int = Field(..., description="整个文件的长度，如果为空文件, 此字段为空")
    md5: str = Field(..., description="文件MD5值，如果为空文件, 此字段为空")
    name: Optional[str] = Field(None, description="文件名称，UTF8编码\\n\\n说明：1、如果为空，在父目录文件下生成版本；2、如果不为空，在父目录下创建文件，同时生成版本")
    ondup: Optional[int] = Field(None, description="仅当name不为空时才会生效\\n- 1:检查是否重命名，重名则抛异常\\n- 2:如果重名冲突，自动重名\\n- 3:如果重名冲突，自动覆盖")
    csflevel: Optional[int] = Field(None, description="文件密级\\n- 0：默认值，创建文件时文件密级设为创建者密级，覆盖版本时不改变密级\\n- 5~15：正常密级\\n- 0x7FFF：空密级")


class FileDuploadRes(BaseModel):
    success: bool = Field(..., description="秒传是否成功,如果为false，忽略其它字段")
    docid: Optional[str] = Field(None, description="文件的gns路径")
    editor: Optional[str] = Field(None, description="编辑者名称，UTF8编码，秒传成功则返回")
    modified: Optional[int] = Field(None, description="上传时间，UTC时间，此为上传版本时的服务器时间，秒传成功则返回")
    name: Optional[str] = Field(None, description="文件名称，UTF8编码")
    rev: Optional[str] = Field(None, description="文件版本号")


class FilePlayinfoReq(BaseModel):
    definition: Optional[str] = Field(None, description="视频画质（音频音质），为空默认为原始画质\\n\\n有效值：空； od；sd\\n\\n注：对于视频，od表示原始画质，sd表示标清；对于音频，sd请求无效")
    docid: str = Field(..., description="文件gns路径（列举协议返回）")
    rev: Optional[str] = Field(None, description="版本号，为空默认获取最新版本")


class FilePlayinfoRes(BaseModel):
    status: int = Field(..., description="转码状态:\\r\\n\\r\\n0 未开始转码；1 正在转码；2转码完成")
    odstat: int = Field(..., description="原始画质（音质）：0无此分辨率；1已转码")
    sdstat: int = Field(..., description="标清：0无此分辨率；1已转码")
    docid: str = Field(..., description="转码文件的唯一标识id，转码完成至少一种分辨率，认为完成转码，返回转码文件的id")
    remaining_time: Optional[int] = Field(None, alias="remainingTime", description="转码剩余时间（秒），status为1时返回")


class FileSendReq(BaseModel):
    docid: str = Field(..., description="要发送的文件gns路径")
    recipients: List[str] = Field(..., description="收件人名字，为用户登录名，UTF8编码")


class FileSendResResult(BaseModel):
    causemsg: str = Field(..., description="发送成功时为空；否则为详细错误信息")
    msg: str = Field(..., description="发送成功时为空；否则为错误信息")
    recipient: str = Field(..., description="收件人名字")
    success: bool = Field(..., description="发送是否成功")


class FileSendRes(BaseModel):
    result: List[FileSendResResult] = Field(..., description="表示多条发送文件的返回信息")


class FileCustomattributevalueReq(BaseModel):
    attributeid: int = Field(..., description="属性ID")


class FileCustomattributevalueResItem(BaseModel):
    id: int = Field(..., description="属性值ID")
    name: str = Field(..., description="属性值名称")
    level: int = Field(..., description="属性值层级")
    child: Optional[List['FileCustomattributevalueResItem']] = Field(None, description="属性值子属性：包括以上三个字段的数组")


class FileGetfilecustomattributeReq(BaseModel):
    docid: str = Field(..., description="文件gns路径")


class FileGetfilecustomattributeRes(BaseModel):
    id: int = Field(..., description="属性唯一ID")
    name: str = Field(..., description="属性名称")
    value: Optional[Union[str, int, List[str]]] = Field(None, description="属性值，类型为：string/int/string array\\n\\ntype等于1、3时value类型为string，0为string array, 其余均为int\\n\\n属性值为空时不存在此字段")
    valueid: Optional[List[int]] = Field(None, description="属性值ID，类型为int/int array\\n\\ntype等于0或1时存在。0时是int array")
    type: int = Field(..., description="属性类型\\n- 0：层级\\n- 1：枚举\\n- 2：数字\\n- 3：文本\\n- 4：时间 （秒）")


class FileGetcommentReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")


class FileGetcommentResComments(BaseModel):
    id: int = Field(..., description="评论id")
    commentator: str = Field(..., description="评论人")
    commentatorid: str = Field(..., description="评论人id")
    answerto: str = Field(..., description="被回复者，无被回复者时，该项为空\\n\\nmode 为 1 或 3 时，返回此项")
    score: int = Field(..., description="评分\\n\\nmode 为 2 或 3 时，返回此项\\n\\n默认为-1，表示未评分")
    comment: str = Field(..., description="评论内容\\n\\nmode 为 1 或 3 时，返回此项\\n\\n默认为空，表示无评分内容")
    time: int = Field(..., description="评论的时间戳，UTC时间，精确到微秒")


class FileGetcommentRes(BaseModel):
    mode: int = Field(..., description="评论模式\\n- 0:未启用评论和评分\\n- 1:只启用评论\\n- 2:只启用评分\\n- 3:同时启用评论和评分")
    averagescore: Optional[float] = Field(None, description="总评分\\n\\nmode 为 2 或 3 时，返回此项")
    hasscored: Optional[bool] = Field(None, description="用户是否已经评分\\n\\nmode 为 2 或 3 时，返回此项")
    comments: Optional[List[FileGetcommentResComments]] = Field(None, description="评论内容的数组\\n\\nmode 不为 0 时，返回此项")


class FileDeletecommentReq(BaseModel):
    commentid: int = Field(..., description="评论id（由获取文件评论协议返回）")
    docid: str = Field(..., description="文件gns路径（列举协议返回）")


class FileConvertpathReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")


class FileConvertpathRes(BaseModel):
    namepath: str = Field(..., description="名字路径")


class FileGetinfobypathReq(BaseModel):
    namepath: str = Field(..., description="名字路径，由顶级入口（个人文档/文档库/群组等）开始的对象全路径，以\"/\"分隔")


class FileGetinfobypathRes(BaseModel):
    docid: str = Field(..., description="文件/目录的gns路径")
    name: str = Field(..., description="文件/目录的名称，UTF8编码")
    rev: str = Field(..., description="文件版本号或目录数据变化标识")
    size: int = Field(..., description="文件的大小，目录大小为-1")
    modified: int = Field(..., description="目录修改时间/文件上传时间，UTC时间，此为文件上传到服务器时间")
    client_mtime: Optional[int] = Field(None, description="如果是文件，返回由客户端设置的文件本地修改时间\\n\\n若未设置，返回modified的值")


class FileSetappmetadataReq(BaseModel):
    appid: str = Field(..., description="应用 id，由控制台配置后分配")
    appmetadata: Dict[str, str] = Field(..., description="- json 格式的应用元数据集合，key-value string 的形式\\n- appmetadata 中只允许 key-value string，对于 value 为 integer、boolen、array 等情况，要求把值转为 string。非 key-value string 的值会被忽略")
    docid: str = Field(..., description="文件gns路径（列举协议返回）")


class FileGetappmetadataReq(BaseModel):
    appid: str = Field(..., description="应用 id，由控制台配置后分配")
    docid: str = Field(..., description="文件gns路径（列举协议返回）")


class FileGetappmetadataResItem(BaseModel):
    appid: str = Field(..., description="应用 id")
    appname: str = Field(..., description="应用名")
    appmetadata: Dict[str, str] = Field(..., description="json 格式的应用元数据集合，key-value string 的形式")


class FileOpstatisticsReq(BaseModel):
    docid: List[str] = Field(..., description="文件gns路径（列举协议返回）")


class FileOpstatisticsValue(BaseModel):
    download: int = Field(..., description="下载量")
    preview: int = Field(..., description="预览量")


class FileOpstatisticsRes(RootModel[Dict[str, FileOpstatisticsValue]]):
    pass


class FileSubmitcommentReq(BaseModel):
    docid: str = Field(..., description="文件gns路径（列举协议返回）")
    answertoid: Optional[str] = Field(None, description="被回复者id ")
    score: Optional[int] = Field(None, description="评分\\n\\n评论模式mode 为 2 或 3 时（包含评分功能），需要此项\\n\\n默认为 -1，表示无评分")
    comment: Optional[str] = Field(None, description="评论内容\\n\\n评论模式mode 为 1 或 3 时（包含评论功能），需要此项\\n\\n默认为空")


class FileSetfilecustomattributeReqAttribute(BaseModel):
    id: int = Field(..., description="属性ID")
    value: Optional[Union[str, int, List[int]]] = Field(None, description="属性值string/int/int array\\n\\ntype等于3时value类型为string，0为int array, 其余均为int\\n\\n注： 时长单位为秒")


class FileSetfilecustomattributeReq(BaseModel):
    docid: Optional[str] = Field(None, description="文件gns路径")
    attribute: List[FileSetfilecustomattributeReqAttribute] = Field(..., description="属性值数组") 