"""
AnyShare API Models

This module provides all the Pydantic models used for API requests and responses.
Models are organized by functionality and can be imported individually or by category.

Usage:
    # Import specific models
    >>> from anyshare_client.api.models import Auth1GetnewReq, Auth1GetnewRes

    # Import all models from a category
    >>> from anyshare_client.api.models.auth_models import *

    # Import common models
    >>> from anyshare_client.api.models import Error, AnyshareApiException

Categories:
    - Common: Error handling and base models
    - Auth: Authentication and authorization models
    - EntryDoc: Document library models
    - Directory: Directory operation models
    - Autolock: File locking models
    - FileTransfer: File upload/download models
    - FileOperation: File metadata and operation models
"""

# === Common Models ===
from .common_models import Error, AnyshareApiException

# === Authentication Models ===
from .auth_models import *

# === EntryDoc Models ===
from .entrydoc_models import *

# === Directory Models ===
from .directory_models import *

# === Autolock Models ===
from .autolock_models import *

# === File Transfer Models ===
from .file_transfer_models import *

# === File Operation Models ===
from .file_operation_models import *
# Note: All models are imported via star imports above.
# The __all__ list below explicitly controls what gets exported when using:
# from anyshare_client.api.models import *

__all__ = [
    "Error",
    "AnyshareApiException",
    # Auth Models
    "DeviceInfoBase",
    "DeviceInfoWithVersion",
    "Auth1GetconfigResDeviceinfo",
    "Auth1GetconfigResThirdauth",
    "Auth1GetconfigResExtapp",
    "Auth1GetconfigResVcodelonginconfig",
    "Auth1GetconfigResOemconfig",
    "Auth1GetconfigResThirdcsfsysconfig",
    "Auth1GetconfigResWindowsadsso",
    "Auth1GetconfigResLimitrateconfig",
    "Auth1GetconfigResDualfactorauthserverstatus",
    "Auth1GetconfigRes",
    "Auth1GetoauthinfoRes",
    "Auth1GetnewReqVcodeinfo",
    "Auth1GetnewReq",
    "Auth1GetnewRes",
    "Auth1GetbythirdpartyReq",
    "Auth1GetbythirdpartyRes",
    "Auth1GetbyticketReq",
    "Auth1GetbyticketRes",
    "Auth1GetbyadsessionReq",
    "Auth1GetbyadsessionRes",
    "Auth1GetextappinfoReq",
    "Auth1GetextappinfoRes",
    "Auth1ExtloginclientReq",
    "Auth1ExtloginclientRes",
    "Auth1RefreshtokenReq",
    "Auth1RefreshtokenRes",
    "Auth1RevoketokenReq",
    "Auth1ModifypasswordReq",
    "Auth1SendauthvcodeReq",
    "Auth1SendauthvcodeRes",
    "Auth1LogoutReq",
    "Auth1ValidatesecuritydeviceReq",
    "Auth1ValidatesecuritydeviceRes",
    "Auth1CheckuninstallpwdReq",
    "Auth1CheckuninstallpwdRes",
    "Auth1GetvcodeReq",
    "Auth1GetvcodeRes",
    "Auth1SelfregistrationReq",
    "Auth1SelfregistrationRes",
    # entrydoc_models
    "Entrydoc2GetReq",
    "Entrydoc2GetResDocinfoSiteinfo",
    "Entrydoc2GetResDocinfo",
    "Entrydoc2GetRes",
    "Entrydoc2GetdocinfoReq",
    "Entrydoc2GetdocinfoRes",
    "Entrydoc2GetmanagedResDocinfo",
    "Entrydoc2GetmanagedRes",
    "Entrydoc2GetuserquotaResQuotainfo",
    "Entrydoc2GetuserquotaRes",
    "Entrydoc2GetdocquotaReq",
    "Entrydoc2GetdocquotaRes",
    # Directory Models
    "DirListResDir",
    "DirListResFile",
    "DirSetcsflevelRes",
    "DirListReq",
    "DirRenameReq",
    "DirIsfileoutboxReq",
    "DirAttributeReq",
    "DirDeletetagReq",
    "DirCopyReq",
    "DirGetsuggestnameReq",
    "DirIsfileoutboxRes",
    "DirAddtagsReq",
    "DirCheckwatermarkRes",
    "DirAddtagReq",
    "DirMoveRes",
    "DirSetcsflevelReq",
    "DirCopyprogressRes",
    "DirCopyprogressReq",
    "DirCreateReq",
    "DirCheckwatermarkReq",
    "DirAttributeRes",
    "DirCreatemultileveldirReq",
    "DirDeleteRes",
    "DirCreatemultileveldirRes",
    "DirListRes",
    "DirMoveReq",
    "DirAddtagsRes",
    "DirCreateRes",
    "DirRenameRes",
    "DirGetsuggestnameRes",
    "DirCopyRes",
    "DirDeleteReq",
    "DirSizeReq",
    "DirSizeRes",
    # Autolock Models
    "AutolockLockReq",
    "AutolockTrylockReq",
    "AutolockTrylockRes",
    "AutolockRefreshReq",
    "AutolockRefreshResLockinfo",
    "AutolockRefreshRes",
    "AutolockUnlockReq",
    "AutolockGetlockinfoReq",
    "AutolockGetlockinfoRes",
    "AutolockGetdirlockinfoReq",
    "AutolockGetdirlockinfoRes",
    "AutolockGetlockedfileinfosReq",
    "AutolockGetlockedfileinfosResDocinfo",
    "AutolockGetlockedfileinfosRes",
    # File Transfer Models
    "FileOsdownloadReq",
    "FileOsdownloadRes",
    "FileOsoptionRes",
    "FileOsbeginuploadReq",
    "FileOsbeginuploadRes",
    "FileOsenduploadReq",
    "FileOsenduploadRes",
    "FileOsinitmultiuploadReq",
    "FileOsinitmultiuploadRes",
    "FileOsuploadpartReq",
    "FileOsuploadpartRes",
    "FileOscompleteuploadReq",
    "FileOscompleteuploadRes",
    "FileOsuploadrefreshReq",
    "FileOsuploadrefreshRes",
    "FileBatchdownloadReq",
    "FileBatchdownloadRes",
    # File Operation Models
    "FileAddtagReq",
    "FileAddtagsReq",
    "FileAddtagsRes",
    "FileAttributeReq",
    "FileAttributeRes",
    "FileCopyReq",
    "FileCopyRes",
    "FileConvertpathReq",
    "FileConvertpathRes",
    "FileCustomattributevalueReq",
    "FileCustomattributevalueResItem",
    "FileDeleteReq",
    "FileDeletecommentReq",
    "FileDeletetagReq",
    "FileDuploadReq",
    "FileDuploadRes",
    "FileGetsuggestnameReq",
    "FileGetsuggestnameRes",
    "FileGetcommentReq",
    "FileGetcommentRes",
    "FileGetcommentResComments",
    "FileGetfilecustomattributeReq",
    "FileGetfilecustomattributeRes",
    "FileGetinfobypathReq",
    "FileGetinfobypathRes",
    "FileGetappmetadataReq",
    "FileGetappmetadataResItem",
    "FileMetadataReq",
    "FileMetadataRes",
    "FileMoveReq",
    "FileMoveRes",
    "FileOpstatisticsReq",
    "FileOpstatisticsRes",
    "FileOpstatisticsValue",
    "FilePlayinfoReq",
    "FilePlayinfoRes",
    "FilePreduploadReq",
    "FilePreduploadRes",
    "FilePreviewossReq",
    "FilePreviewossRes",
    "FileRenameReq",
    "FileRenameRes",
    "FileRestorerevisionReq",
    "FileRestorerevisionRes",
    "FileRevisionsReq",
    "FileRevisionsRes",
    "FileSendReq",
    "FileSendRes",
    "FileSendResResult",
    "FileSetappmetadataReq",
    "FileSetcsflevelReq",
    "FileSetcsflevelRes",
    "FileSetfilecustomattributeReq",
    "FileSetfilecustomattributeReqAttribute",
    "FileSubmitcommentReq",
]