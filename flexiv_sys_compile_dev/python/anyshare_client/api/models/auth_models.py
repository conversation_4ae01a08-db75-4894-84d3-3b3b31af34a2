from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from .common_models import Error

# --- Nested Device Info Models (Common Structure) ---
class DeviceInfoBase(BaseModel):
    """设备基本信息"""
    name: Optional[str] = Field(None, description="设备名称")
    ostype: Optional[int] = Field(None, description="操作系统类型\n0：Unknown\n1：IOS\n2：Android\n3：Windows phone\n4：Windows\n5：MacOSX\n6：Web\n7：MobileWeb")
    devicetype: Optional[str] = Field(None, description="设备硬件类型，自定义。如：iphone5s, ipad, 联想一体机, mac")
    udid: Optional[str] = Field(None, description="设备唯一标识号，windows下取mac地址, ios取udid, web为空")

class DeviceInfoWithVersion(DeviceInfoBase):
    """带版本的设备信息"""
    version: Optional[str] = Field(None, description="客户端程序的版本\nWindows： **********形式（主版本号，小版本号，修订号，构建号）\nIOS：*******\nAndroid：*********")

# --- /auth1/getconfig ---
class Auth1GetconfigResDeviceinfo(BaseModel):
    """设备授权信息"""
    auth_days: int = Field(..., description="授权天数")
    auth_status: int = Field(..., description="授权状态\n1未授权\n2已授权\n3已过期\n4已失效")
    auth_type: int = Field(..., description="授权类型\n1未授权\n2测试授权\n3正式授权")
    hardware_type: str = Field(..., description="设备型号")
    software_type: str = Field(..., description="当前产品类型信息\nEDMS: 涉密一体机\nASE：AnyShare Enterprise\nASS：AnyShare Express\nASC：AnyShare Cloud")

class Auth1GetconfigResThirdauth(BaseModel):
    """第三方认证配置"""
    id: str = Field(..., description="唯一第三方认证系统")
    config: Dict[str, Any] = Field(..., description="第三方认证系统的配置参数")

class Auth1GetconfigResExtapp(BaseModel):
    """外部应用配置信息"""
    enable_chaojibiaoge: bool = Field(..., description="是否启用外部应用超级表格，默认为false")

class Auth1GetconfigResVcodelonginconfig(BaseModel):
    """登录验证码配置信息"""
    isenable: bool = Field(..., description="是否启用登录验证码功能")
    passwderrcnt: int = Field(..., description="达到开启登录验证码的密码出错次数")

class Auth1GetconfigResOemconfig(BaseModel):
    """OEM配置信息"""
    allowauthlowcsfuser: bool = Field(..., description="权限配置，是否允许给密级低于文件密级的用户配置权限")
    allowowner: bool = Field(..., description="权限配置，是否允许配置所有者")
    cadpreview: bool = Field(..., description="是否启用CAD预览服务")
    clearcache: bool = Field(..., description="客户端退出时，是否强制清除缓存")
    clientlogouttime: int = Field(..., description="客户端超时退出时间，单位为分钟\n-1表示不开启超时退出\n其它值表示多少分钟后退出\n例如30表示30分钟超时后退出")
    defaultpermexpireddays: int = Field(..., description="客户端配置权限时，默认权限有效天数\n-1为无限期")
    enableclientmanuallogin: bool = Field(..., description="是否允许AnyShare客户端手动登录")
    enablecsflevel: bool = Field(..., description="是否开启更改密级审核\ntrue表示开启\nfalse表示关闭")
    enablefiletransferlimit: bool = Field(..., description="是否开启文件传输限制功能")
    enablehttplinkaudit: bool = Field(..., description="是否开启http共享审核\ntrue表示开启\nfalse表示关闭")
    enableonedrive: bool = Field(..., description="是否启用onedrive跳转，默认为false")
    enableshareaudit: bool = Field(..., description="是否开启共享审核\ntrue表示开启\nfalse表示关闭")
    enableuseragreement: bool = Field(..., description="是否显示用户协议，默认为false")
    hidecachesetting: bool = Field(..., description="客户端是否隐藏缓存设置")
    indefiniteperm: bool = Field(..., description="权限配置，截至时间是否支持无限期")
    maxpassexpireddays: int = Field(..., description="密码配置，密码允许的最大过期天数\n-1为永久")
    owasurl: str = Field(..., description="OWAS的预览url，如果未配置则为空字符串")
    rememberpass: bool = Field(..., description="登录配置，是否允许记住用户名和密码")
    sursenpreview: bool = Field(..., description="是否开启gd/sep预览")
    wopiurl: str = Field(..., description="WOPI的url，如果未配置则为空字符串")
    cadtool: Optional[str] = Field(None, description="配置的转换服务类型，cadpreview为true时返回\n\"mx\"为梦想CAD\n\"hc\"为浩辰CAD")
    cadurl: Optional[str] = Field(None, description="CAD服务器地址\ncadpreview为true，且cadtool为\"hc\"时返回")

class Auth1GetconfigResThirdcsfsysconfig(BaseModel):
    """第三方标密系统配置"""
    id: str = Field(..., description="第三方标密系统唯一标识")
    only_upload_classified: bool = Field(..., description="是否仅上传已标密文件")
    only_share_classified: bool = Field(..., description="是否仅共享已标密文件")
    auto_match_doc_classfication: bool = Field(..., description="是否自动识别文件密级")

class Auth1GetconfigResWindowsadsso(BaseModel):
    """windows ad单点登录相关配置信息"""
    is_enabled: bool = Field(..., description="是否开启了windows ad单点登录")

class Auth1GetconfigResLimitrateconfig(BaseModel):
    """限速配置信息"""
    isenabled: bool = Field(..., description="是否开启网络限速")
    limittype: int = Field(..., description="限速类型\n0 用户级别限速\n1 用户组总体限速")

class Auth1GetconfigResDualfactorauthserverstatus(BaseModel):
    """双因子认证配置信息"""
    auth_by_OTP: bool = Field(..., description="是否使用动态密保验证")
    auth_by_Ukey: bool = Field(..., description="是否使用Ukey验证")
    auth_by_email: bool = Field(..., description="是否使用邮箱验证")
    auth_by_sms: bool = Field(..., description="是否使用短信验证")

class Auth1GetconfigRes(BaseModel):
    """获取服务器配置信息 响应"""
    api_version: str = Field(..., description="API版本，如6.0.8")
    auto_lock_remind: bool = Field(..., description="是否开启文件锁提醒，默认开启")
    cad_plugin_threshold: int = Field(..., description="浩辰CAD使用大图插件的临界值")
    csf_level_enum: Dict[str, int] = Field(..., description="密级枚举信息")
    device_info: Auth1GetconfigResDeviceinfo = Field(..., description="设备信息")
    dualfactor_auth_server_status: Auth1GetconfigResDualfactorauthserverstatus = Field(..., description="双因子认证配置信息")
    enable_doc_comment: bool = Field(..., description="是否开启文件评论功能")
    enable_doc_due_remind: bool = Field(..., description="是否开启文件到期提醒")
    enable_exit_pwd: bool = Field(..., description="是否使用退出口令")
    enable_invitation_share: bool = Field(..., description="是否开启共享邀请链接")
    enable_link_access_code: bool = Field(..., description="是否启用提取码")
    enable_message_notify: bool = Field(..., description="是否启用消息通知功能")
    enable_outlink_watermark: bool = Field(..., description="是否允许用户设置外链水印")
    enable_secret_mode: bool = Field(..., description="是否开启涉密模式")
    enable_set_folder_security_level: bool = Field(..., description="是否允许设置文件夹密级")
    enable_strong_pwd: bool = Field(..., description="是否开启强密码配置")
    entrydoc_view_config: int = Field(..., description="返回入口文档视图模式\n1 代表旧视图\n2 代表新视图（默认）")
    extapp: Auth1GetconfigResExtapp = Field(..., description="外部应用配置信息")
    file_crawl_status: bool = Field(..., description="是否开启文件抓取")
    forbid_ostype: str = Field(..., description="按bit位计算禁止的操作系统类型")
    https: bool = Field(..., description="客户端是否使用https")
    id_card_login_status: bool = Field(..., description="是否使用身份证登录")
    internal_link_prefix: str = Field(..., description="内链地址的前缀")
    limit_rate_config: Auth1GetconfigResLimitrateconfig = Field(..., description="限速配置信息")
    only_share_to_user: bool = Field(..., description="是否只允许共享给用户")
    server_version: str = Field(..., description="服务器版本信息")
    show_knowledge_page: int = Field(..., description="是否显示知识主页")
    strong_pwd_length: int = Field(..., description="强密码最小长度")
    tag_max_num: int = Field(..., description="标签的最大数量")
    third_pwd_modify_url: str = Field(..., description="第三方用户密码修改地址")
    vcode_login_config: Auth1GetconfigResVcodelonginconfig = Field(..., description="登录验证码配置信息")
    windows_ad_sso: Auth1GetconfigResWindowsadsso = Field(..., description="windows ad单点登录相关配置信息")
    oemconfig: Auth1GetconfigResOemconfig = Field(..., description="oem配置信息")
    thirdauth: Optional[Auth1GetconfigResThirdauth] = Field(None, description="表示开启了第三方认证，如果未开启，则不会有")
    third_csfsys_config: Optional[Auth1GetconfigResThirdcsfsysconfig] = Field(None, description="第三方标密系统配置，如果未开启，则不会有")
    custome_application_config: Optional[str] = Field(None, description="定制化的客户应用配置，用于给客户端做定制化功能的开关，格式为Json字符串。如：{\"appid\": \"CUFE\"}")

# --- /auth1/getoauthinfo ---
class Auth1GetoauthinfoRes(BaseModel):
    """获取OAuth信息 响应"""
    isenabled: bool = Field(..., description="是否开启")
    authurl: str = Field(..., description="完整的OAuth认证地址")
    authserver: str = Field(..., description="认证服务器地址")
    redirectserver: str = Field(..., description="第三方认证完毕后，跳转到AnyShare服务器的地址")

# --- /auth1/getnew ---
class Auth1GetnewReqVcodeinfo(BaseModel):
    """登录验证码信息"""
    uuid: Optional[str] = Field(None, description="验证码唯一标识")
    vcode: Optional[str] = Field(None, description="验证码字符串")
    ismodify: Optional[bool] = Field(None, description="修改密码界面标识符")

class Auth1GetnewReq(BaseModel):
    """登录（标准） 请求"""
    account: str = Field(..., description="用户登录账号")
    password: str = Field(..., description="加密后的密文")
    deviceinfo: Optional[DeviceInfoWithVersion] = Field(None, description="设备信息")
    vcodeinfo: Optional[Auth1GetnewReqVcodeinfo] = Field(None, description="登录验证码信息")
    dualfactorauthinfo: Optional[Dict[str, Any]] = Field(None, description="双因子登录的验证信息")

class Auth1GetnewRes(BaseModel):
    """登录（标准） 响应"""
    userid: str = Field(..., description="唯一标识用户的ID")
    tokenid: str = Field(..., description="与userid一起验证请求的合法性")
    needmodifypassword: bool = Field(..., description="True表示该用户需要修改密码后才能登陆\nFalse表示该用户不需要修改密码")
    expires: int = Field(..., description="获取到的token的有效期，单位为秒")

# --- /auth1/getbythirdparty ---
class Auth1GetbythirdpartyReq(BaseModel):
    """登录（使用第三方凭证） 请求"""
    thirdpartyid: str = Field(..., description="标识第三方认证类型")
    params: Dict[str, Any] = Field(..., description="保存第三方认证系统相关的参数")
    deviceinfo: DeviceInfoBase = Field(..., description="设备信息")

class Auth1GetbythirdpartyRes(BaseModel):
    """登录（使用第三方凭证） 响应"""
    userid: str = Field(..., description="唯一标识用户的ID")
    tokenid: str = Field(..., description="与userid一起验证请求的合法性")
    expires: int = Field(..., description="获取到的token的有效期，单位为秒")

# --- /auth1/getbyticket ---
class Auth1GetbyticketReq(BaseModel):
    """登录（西电ticket） 请求"""
    ticket: str = Field(..., description="auth服务器返回的ticket票据，用来向token服务器请求token")
    service: str = Field(..., description="请求ticket时的service名称")

class Auth1GetbyticketRes(BaseModel):
    """登录（西电ticket） 响应"""
    userid: str = Field(..., description="唯一标识用户的ID")
    tokenid: str = Field(..., description="与userid一起验证请求的合法性")
    expires: int = Field(..., description="获取到的token的有效期，单位为秒")

# --- /auth1/getbyadsession ---
class Auth1GetbyadsessionReq(BaseModel):
    """登录（使用windows登录凭据） 请求"""
    adsession: str = Field(..., description="windows ad用户登录凭据")

class Auth1GetbyadsessionRes(BaseModel):
    """登录（使用windows登录凭据） 响应"""
    userid: str = Field(..., description="唯一标识用户的ID")
    tokenid: str = Field(..., description="与userid一起验证请求的合法性")
    expires: int = Field(..., description="获取到的token的有效期，单位为秒")

# --- /auth1/getextappinfo ---
class Auth1GetextappinfoReq(BaseModel):
    """登录外部应用（集成到anyshare） 请求"""
    apptype: int = Field(..., description="外部应用标识")
    params: Dict[str, Any] = Field(..., description="应用系统相关配置")

class Auth1GetextappinfoRes(BaseModel):
    """登录外部应用（集成到anyshare） 响应"""
    value: Dict[str, Any] = Field(..., description="返回登录信息")

# --- /auth1/extloginclient ---
class Auth1ExtloginclientReq(BaseModel):
    """登录（信任的第三方应用appid） 请求"""
    account: str = Field(..., description="用户登录账号（不能使用admin登录）")
    appid: str = Field(..., description="爱数分配给第三方系统的应用id")
    key: str = Field(..., description="组合appid，appkey，account后进行md5算法后得到（不区分大小写）")
    deviceinfo: Optional[DeviceInfoBase] = Field(None, description="设备信息\n注意：\n1、传参deviceinfo时，需要同时提供name、ostype、devicetype、udid参数\n2、不确定操作系统类型时，ostype 可以传递 0")

class Auth1ExtloginclientRes(BaseModel):
    """登录（信任的第三方应用appid） 响应"""
    userid: str = Field(..., description="唯一标识用户的ID")
    tokenid: str = Field(..., description="与userid一起验证请求的合法性")
    expires: int = Field(..., description="获取到的token的有效期，单位为秒")

# --- /auth1/refreshtoken ---
class Auth1RefreshtokenReq(BaseModel):
    """刷新身份凭证有效期 请求"""
    tokenid: str = Field(..., description="用户身份凭证")
    expirestype: int = Field(..., description="刷新有效期类型：\nexpirestype等于1时，刷新后token有效期为3天；\nexpirestype等于2时，刷新后token有效期为1年；\nexpirestype为其他值时，抛错参数值非法。")
    userid: str = Field(..., description="用户id")

class Auth1RefreshtokenRes(BaseModel):
    """刷新身份凭证有效期 响应"""
    expires: int = Field(..., description="刷新以后token的有效期，单位为秒")

# --- /auth1/revoketoken ---
class Auth1RevoketokenReq(BaseModel):
    """回收身份凭证 请求"""
    tokenid: str = Field(..., description="用户身份凭证")

# --- /auth1/modifypassword ---
class Auth1ModifypasswordReq(BaseModel):
    """修改用户密码 请求"""
    account: str = Field(..., description="用户登录名")
    oldpwd: str = Field(..., description="用户旧密码（RSA加密+Base64）")
    newpwd: str = Field(..., description="用户新密码（RSA加密+Base64）")

# --- /auth1/sendauthvcode ---
class Auth1SendauthvcodeReq(BaseModel):
    """发送短信验证码（双因子登录） 请求"""
    account: str = Field(..., description="用户登录账号")
    password: str = Field(..., description="加密后的密文")
    oldtelnum: str = Field(..., description="上一次的获取的手机号（处理管理员修改手机号的情况）")
    deviceinfo: Optional[DeviceInfoWithVersion] = Field(None, description="设备信息")

class Auth1SendauthvcodeRes(BaseModel):
    """发送短信验证码（双因子登录） 响应"""
    authway: str = Field(..., description="用户手机号")
    sendinterval: int = Field(..., description="短信发送验证码的间隔（单位/秒）")
    isduplicatesended: bool = Field(..., description="是否重复发送了\ntrue表示在时间间隔内重复发送了\nfalse表示未重复发送")

# --- /auth1/logout ---
class Auth1LogoutReq(BaseModel):
    """登出 请求"""
    ostype: int = Field(..., description="操作系统类型\n0: Unknown\n1: Ios\n2: Android\n3: Windows phone\n4: Windows\n5: MacOSX\n6: Web\n7: Mobile Web\n8: NAS")

# --- /auth1/validatesecuritydevice ---
class Auth1ValidatesecuritydeviceReq(BaseModel):
    """二次安全设备认证 请求"""
    thirdpartyid: str = Field(..., description="标识第三方认证类型")
    params: Dict[str, Any] = Field(..., description="第三方认证信息")

class Auth1ValidatesecuritydeviceRes(BaseModel):
    """二次安全设备认证 响应"""
    result: bool = Field(..., description="是否认证成功")

# --- /auth1/checkuninstallpwd ---
class Auth1CheckuninstallpwdReq(BaseModel):
    """PC客户端卸载输入口令 请求"""
    uninstallpwd: str = Field(..., description="卸载口令")

class Auth1CheckuninstallpwdRes(BaseModel):
    """PC客户端卸载输入口令 响应"""
    result: str = Field(..., description="接口调用成功，固定返回 'ok'") # Assuming 'ok' is the success indicator

# --- /auth1/getvcode ---
class Auth1GetvcodeReq(BaseModel):
    """获取验证码 请求"""
    uuid: str = Field(..., description="上一条验证码的唯一标识 (如果第一次获取，传空字符串)")

class Auth1GetvcodeRes(BaseModel):
    """获取验证码 响应"""
    uuid: str = Field(..., description="验证码唯一标识")
    vcode: str = Field(..., description="编码后的验证码字符串 (Base64)")

# --- /auth1/selfregistration ---
class Auth1SelfregistrationReq(BaseModel):
    """用户自注册 请求"""
    registerid: str = Field(..., description="注册号")
    certid: str = Field(..., description="身份证号")
    realname: str = Field(..., description="真实姓名")
    password: str = Field(..., description="用户密码（采用RSA加密并将加密结果使用base64编码）")

class Auth1SelfregistrationRes(BaseModel):
    """用户自注册 响应"""
    userid: str = Field(..., description="用户唯一标识")


