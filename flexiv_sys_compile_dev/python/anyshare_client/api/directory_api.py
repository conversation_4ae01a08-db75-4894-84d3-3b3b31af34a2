from .base_api import BaseApi
from anyshare_client.config import Config
from .models import *
from typing import Optional, Union

class DirectoryApi(BaseApi):
    def __init__(self, config: Config):
        super().__init__(config)

    def create_directory(self, req: DirCreateReq) -> Optional[Union[DirCreateRes, Error, str]]:
        """创建目录协议"""
        return self.api_request(
            relative_path="/dir/create",
            req_data=req,
            success_model=DirCreateRes
        )

    def create_multilevel_directory(self, req: DirCreatemultileveldirReq) -> Optional[Union[DirCreatemultileveldirRes, Error, str]]:
        """创建多级目录协议"""
        return self.api_request(
            relative_path="/dir/createmultileveldir",
            req_data=req,
            success_model=DirCreatemultileveldirRes
        )

    def delete_directory(self, req: DirDeleteReq) -> Optional[Union[DirDeleteRes, Error, str]]:
        """删除目录协议"""
        return self.api_request(
            relative_path="/dir/delete",
            req_data=req,
            success_model=DirDeleteRes
        )

    def rename_directory(self, req: DirRenameReq) -> Optional[Union[DirRenameRes, Error, str]]:
        """重命名目录协议"""
        return self.api_request(
            relative_path="/dir/rename",
            req_data=req,
            success_model=DirRenameRes
        )

    def list_directory_contents(self, req: DirListReq) -> Optional[Union[DirListRes, Error, str]]:
        """浏览目录协议"""
        return self.api_request(
            relative_path="/dir/list",
            req_data=req,
            success_model=DirListRes
        )

    def move_directory(self, req: DirMoveReq) -> Optional[Union[DirMoveRes, Error, str]]:
        """移动目录协议"""
        return self.api_request(
            relative_path="/dir/move",
            req_data=req,
            success_model=DirMoveRes
        )

    def copy_directory(self, req: DirCopyReq) -> Optional[Union[DirCopyRes, Error, str]]:
        """复制目录协议"""
        return self.api_request(
            relative_path="/dir/copy",
            req_data=req,
            success_model=DirCopyRes
        )

    def get_copy_progress(self, req: DirCopyprogressReq) -> Optional[Union[DirCopyprogressRes, Error, str]]:
        """复制目录进度查询协议"""
        return self.api_request(
            relative_path="/dir/copyprogress",
            req_data=req,
            success_model=DirCopyprogressRes
        )

    def get_suggest_name(self, req: DirGetsuggestnameReq) -> Optional[Union[DirGetsuggestnameRes, Error, str]]:
        """获取创建目录的建议名称"""
        return self.api_request(
            relative_path="/dir/getsuggestname",
            req_data=req,
            success_model=DirGetsuggestnameRes
        )

    def add_directory_tag(self, req: DirAddtagReq) -> Optional[Union[bool, Error, str]]:
        """添加目录标签"""
        response = self.api_request(
            relative_path="/dir/addtag",
            req_data=req,
            success_model=None
        )
        if response is None:
            return True
        return response

    def add_directory_tags(self, req: DirAddtagsReq) -> Optional[Union[DirAddtagsRes, Error, str]]:
        """添加目录多个标签"""
        return self.api_request(
            relative_path="/dir/addtags",
            req_data=req,
            success_model=DirAddtagsRes
        )

    def delete_directory_tag(self, req: DirDeletetagReq) -> Optional[Union[bool, Error, str]]:
        """删除目录标签"""
        response = self.api_request(
            relative_path="/dir/deletetag",
            req_data=req,
            success_model=None
        )
        if response is None:
            return True
        return response

    def get_directory_attributes(self, req: DirAttributeReq) -> Optional[Union[DirAttributeRes, Error, str]]:
        """获取目录属性协议"""
        return self.api_request(
            relative_path="/dir/attribute",
            req_data=req,
            success_model=DirAttributeRes
        )

    def get_directory_size(self, req: DirSizeReq) -> Optional[Union[DirSizeRes, Error, str]]:
        """获取目录大小协议"""
        return self.api_request(
            relative_path="/dir/size",
            req_data=req,
            success_model=DirSizeRes
        )

    def set_directory_csf_level(self, req: DirSetcsflevelReq) -> Optional[Union[DirSetcsflevelRes, Error, str]]:
        """设置目录密级"""
        return self.api_request(
            relative_path="/dir/setcsflevel",
            req_data=req,
            success_model=DirSetcsflevelRes
        )

    def is_file_outbox(self, req: DirIsfileoutboxReq) -> Optional[Union[DirIsfileoutboxRes, Error, str]]:
        """检查是否是发件箱协议"""
        return self.api_request(
            relative_path="/dir/isfileoutbox",
            req_data=req,
            success_model=DirIsfileoutboxRes
        )

    def check_directory_watermark(self, req: DirCheckwatermarkReq) -> Optional[Union[DirCheckwatermarkRes, Error, str]]:
        """检查是否需要下载水印"""
        return self.api_request(
            relative_path="/dir/checkwatermark",
            req_data=req,
            success_model=DirCheckwatermarkRes
        )