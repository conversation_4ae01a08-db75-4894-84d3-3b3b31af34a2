from typing import Optional, Dict, Union, List

from anyshare_client.config import Config
from .base_api import BaseApi
from .models import *

class FileOperationApi(BaseApi):
    """
    文件操作相关API
    """
    def __init__(self, config: Config):
        super().__init__(config)

    def delete_file(self, req_data: FileDeleteReq) -> Optional[Union[bool, Error, str]]:
        """
        删除文件协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[bool, Error, str]]: API响应，成功时可能为True，错误时可能为Error对象或错误信息字符串。
        
        API Path: /file/delete
        HTTP Method: POST
        """
        response = self.api_request(
            relative_path="/file/delete",
            req_data=req_data,
            success_model=None
        )
        if response is None:
            return True
        return response

    def rename_file(self, req_data: FileRenameReq) -> Optional[Union[FileRenameRes, Error, str]]:
        """
        重命名文件协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileRenameRes, Error, str]]: API响应，包含重命名结果或错误信息。
        
        API Path: /file/rename
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/rename",
            req_data=req_data,
            success_model=FileRenameRes
        )

    def move_file(self, req_data: FileMoveReq) -> Optional[Union[FileMoveRes, Error, str]]:
        """
        移动文件协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileMoveRes, Error, str]]: API响应，包含移动结果或错误信息。

        API Path: /file/move
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/move",
            req_data=req_data,
            success_model=FileMoveRes
        )

    def copy_file(self, req_data: FileCopyReq) -> Optional[Union[FileCopyRes, Error, str]]:
        """
        复制文件协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileCopyRes, Error, str]]: API响应，包含复制结果或错误信息。

        API Path: /file/copy
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/copy",
            req_data=req_data,
            success_model=FileCopyRes
        )

    def add_file_tag(self, req_data: FileAddtagReq) -> Optional[Union[bool, Error, str]]:
        """
        添加文件标签

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[bool, Error, str]]: API响应，成功时可能为True，错误时可能为Error对象或错误信息字符串。

        API Path: /file/addtag
        HTTP Method: POST
        """
        response = self.api_request(
            relative_path="/file/addtag",
            req_data=req_data,
            success_model=None
        )
        if response is None:
            return True
        return response

    def add_file_tags(self, req_data: FileAddtagsReq) -> Optional[Union[FileAddtagsRes, Error, str]]:
        """
        添加文件多个标签

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileAddtagsRes, Error, str]]: API响应，包含添加多个标签的结果或错误信息。

        API Path: /file/addtags
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/addtags",
            req_data=req_data,
            success_model=FileAddtagsRes
        )

    def delete_file_tag(self, req_data: FileDeletetagReq) -> Optional[Union[bool, Error, str]]:
        """
        删除文件标签

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[bool, Error, str]]: API响应，成功时可能为True，错误时可能为Error对象或错误信息字符串。

        API Path: /file/deletetag
        HTTP Method: POST
        """
        response = self.api_request(
            relative_path="/file/deletetag",
            req_data=req_data,
            success_model=None
        )
        if response is None:
            return True
        return response

    def get_info_by_path(self, req_data: FileGetinfobypathReq) -> Optional[Union[FileGetinfobypathRes, Error, str]]:
        """
        由名字路径获取对象信息协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileGetinfobypathRes, Error, str]]: API响应，包含对象信息或错误信息。

        API Path: /file/getinfobypath
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/getinfobypath",
            req_data=req_data,
            success_model=FileGetinfobypathRes
        )

    def convert_path(self, req_data: FileConvertpathReq) -> Optional[Union[FileConvertpathRes, Error, str]]:
        """
        转换路径协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileConvertpathRes, Error, str]]: API响应，包含转换后的路径信息或错误信息。

        API Path: /file/convertpath
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/convertpath",
            req_data=req_data,
            success_model=FileConvertpathRes
        )

    def get_file_revisions(self, req_data: FileRevisionsReq) -> Optional[Union[List[FileRevisionsRes], Error, str]]:
        """
        获取文件历史版本协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[List[FileRevisionsRes], Error, str]]: API响应，包含文件历史版本列表或错误信息。

        API Path: /file/revisions
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/revisions",
            req_data=req_data,
            success_model=FileRevisionsRes
        )

    def restore_file_revision(self, req_data: FileRestorerevisionReq) -> Optional[Union[FileRestorerevisionRes, Error, str]]:
        """
        还原文件历史版本协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileRestorerevisionRes, Error, str]]: API响应，包含还原结果或错误信息。

        API Path: /file/restorerevision
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/restorerevision",
            req_data=req_data,
            success_model=FileRestorerevisionRes
        )

    def get_file_metadata(self, req_data: FileMetadataReq) -> Optional[Union[FileMetadataRes, Error, str]]:
        """
        获取元数据协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileMetadataRes, Error, str]]: API响应，包含文件元数据或错误信息。

        API Path: /file/metadata
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/metadata",
            req_data=req_data,
            success_model=FileMetadataRes
        )

    def get_file_attribute(self, req_data: FileAttributeReq) -> Optional[Union[FileAttributeRes, Error, str]]:
        """
        获取文件属性协议

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileAttributeRes, Error, str]]: API响应，包含文件属性或错误信息。

        API Path: /file/attribute
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/attribute",
            req_data=req_data,
            success_model=FileAttributeRes
        )

    def set_file_csf_level(self, req_data: FileSetcsflevelReq) -> Optional[Union[FileSetcsflevelRes, Error, str]]:
        """
        设置文件密级协议
        API Path: /file/setcsflevel
        """
        return self.api_request(
            relative_path="/file/setcsflevel",
            req_data=req_data,
            success_model=FileSetcsflevelRes
        )

    def preview_file_oss(self, req_data: FilePreviewossReq) -> Optional[Union[FilePreviewossRes, Error, str]]:
        """
        获取文件预览信息协议 (OSS)
        API Path: /file/previewoss
        """
        return self.api_request(
            relative_path="/file/previewoss",
            req_data=req_data,
            success_model=FilePreviewossRes
        )

    def get_suggest_name_for_file(self, req_data: FileGetsuggestnameReq) -> Optional[Union[FileGetsuggestnameRes, Error, str]]:
        """
        获取文件建议名称协议
        API Path: /file/getsuggestname (Note: model is FileGetsuggestnameReq/Res, path might be shared with dir)
        """
        return self.api_request(
            relative_path="/file/getsuggestname",
            req_data=req_data,
            success_model=FileGetsuggestnameRes
        )

    def get_second_pass_upload_status(self, req_data: FilePreduploadReq) -> Optional[Union[FilePreduploadRes, Error, str]]:
        """
        文件秒传接口（检查是否存在）
        API Path: /file/predupload
        """
        return self.api_request(
            relative_path="/file/predupload",
            req_data=req_data,
            success_model=FilePreduploadRes
        )

    def direct_upload_file_complete(self, req_data: FileDuploadReq) -> Optional[Union[FileDuploadRes, Error, str]]:
        """
        文件直接上传完成（秒传）
        API Path: /file/dupload
        """
        return self.api_request(
            relative_path="/file/dupload",
            req_data=req_data,
            success_model=FileDuploadRes
        )

    def get_custom_attribute_value(self, req_data: FileCustomattributevalueReq) -> Optional[Union[List[FileCustomattributevalueResItem], Error, str]]:
        """
        获取属性属性值

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[List[FileCustomattributevalueResItem], Error, str]]: API响应，包含属性值列表或错误信息。

        API Path: /file/customattributevalue
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/customattributevalue",
            req_data=req_data,
            success_model=FileCustomattributevalueResItem
        )

    def get_file_custom_attribute(self, req_data: FileGetfilecustomattributeReq) -> Optional[Union[List[FileGetfilecustomattributeRes], Error, str]]:
        """
        获取文件自定义属性

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[List[FileGetfilecustomattributeRes], Error, str]]: API响应，包含文件自定义属性列表或错误信息。

        API Path: /file/getfilecustomattribute
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/getfilecustomattribute",
            req_data=req_data,
            success_model=FileGetfilecustomattributeRes
        )

    def set_app_metadata(self, req_data: FileSetappmetadataReq) -> Optional[Union[bool, Error, str]]:
        """
        设置应用元数据

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[bool, Error, str]]: API响应，成功时可能为True，错误时可能为Error对象或错误信息字符串。

        API Path: /file/setappmetadata
        HTTP Method: POST
        """
        response = self.api_request(
            relative_path="/file/setappmetadata",
            req_data=req_data,
            success_model=None
        )
        if response is None:
            return True
        return response

    def get_app_metadata(self, req_data: FileGetappmetadataReq) -> Optional[Union[List[FileGetappmetadataResItem], Error, str]]:
        """
        获取应用元数据

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[List[FileGetappmetadataResItem], Error, str]]: API响应，包含应用元数据列表或错误信息。

        API Path: /file/getappmetadata
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/getappmetadata",
            req_data=req_data,
            success_model=FileGetappmetadataResItem
        )

    def get_op_statistics(self, req_data: FileOpstatisticsReq) -> Optional[Union[FileOpstatisticsRes, Error, str]]:
        """
        批量获取文件操作统计

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[FileOpstatisticsRes, Error, str]]: API响应，包含文件操作统计信息或错误信息。

        API Path: /file/opstatistics
        HTTP Method: POST
        """
        return self.api_request(
            relative_path="/file/opstatistics",
            req_data=req_data,
            success_model=FileOpstatisticsRes
        )

    def set_file_custom_attribute(self, req_data: FileSetfilecustomattributeReq) -> Optional[Union[bool, Error, str]]:
        """
        设置文件属性值

        Args:
            req_data: 请求参数

        Returns:
            Optional[Union[bool, Error, str]]: API响应，成功时可能为True，错误时可能为Error对象或错误信息字符串。

        API Path: /file/setfilecustomattribute
        HTTP Method: POST
        """
        response = self.api_request(
            relative_path="/file/setfilecustomattribute",
            req_data=req_data,
            success_model=None
        )
        if response is None:
            return True
        return response 