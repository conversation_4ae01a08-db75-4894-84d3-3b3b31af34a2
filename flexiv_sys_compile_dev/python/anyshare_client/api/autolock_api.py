from typing import Optional, Union, Any, List
from .base_api import Base<PERSON><PERSON>
from anyshare_client.config import Config
# BaseModel will be used for type hinting and Pydantic handles serialization
from .models import *

class AutolockApi(BaseApi):
    """文件锁管理 API"""

    def __init__(self, config: Config):
        super().__init__(config)

    def lock_file(self, req: AutolockLockReq) -> Optional[Union[bool, Error, str]]:
        """锁定文件 (对应 /autolock/lock)

        文件只能被一个具有写权限的用户锁定。
        成功时返回 True，否则返回 Error 对象或错误字符串。
        """
        response = self.api_request(
            relative_path="/autolock/lock",
            req_data=req,
            success_model=None # Expecting no body on success, api_request returns None for 200 with no body/model
        )
        if response is None: # Successfully executed with no specific response model expected
            return True
        return response # Returns Error or str if not None

    def try_lock_file(self, req: AutolockTrylockReq) -> Optional[Union[AutolockTrylockRes, Error, str]]:
        """尝试锁定文件 (对应 /autolock/trylock)

        trylock时文件未锁定，则锁定文件；文件已被锁定时，返回锁定者的信息。
        """
        return self.api_request(
            relative_path="/autolock/trylock",
            req_data=req,
            success_model=AutolockTrylockRes
        )

    def refresh_file_lock(self, req: AutolockRefreshReq) -> Optional[Union[AutolockRefreshRes, Error, str]]:
        """刷新文件锁 (对应 /autolock/refresh)

        刷新文件锁的最后访问时间。
        """
        return self.api_request(
            relative_path="/autolock/refresh",
            req_data=req,
            success_model=AutolockRefreshRes
        )

    def unlock_file(self, req: AutolockUnlockReq) -> Optional[Union[bool, Error, str]]:
        """解锁文件 (对应 /autolock/unlock)

        只有锁定文件的用户才能对文件解锁。
        成功时返回 True。
        """
        response = self.api_request(
            relative_path="/autolock/unlock",
            req_data=req,
            success_model=None # Expecting no body on success
        )
        if response is None:
            return True
        return response # Returns Error or str

    def get_lock_info(self, req: AutolockGetlockinfoReq) -> Optional[Union[AutolockGetlockinfoRes, Error, str]]:
        """获取文件锁信息 (对应 /autolock/getlockinfo)

        备注：只有在文件被锁定的情况下，才会返回有效的lockerid、lockeraccount、lockname等信息。
        """
        return self.api_request(
            relative_path="/autolock/getlockinfo",
            req_data=req,
            success_model=AutolockGetlockinfoRes
        )

    def get_dir_lock_info(self, req: AutolockGetdirlockinfoReq) -> Optional[Union[AutolockGetdirlockinfoRes, Error, str]]:
        """获取文件夹锁信息 (对应 /autolock/getdirlockinfo)
        """
        return self.api_request(
            relative_path="/autolock/getdirlockinfo",
            req_data=req,
            success_model=AutolockGetdirlockinfoRes
        )

    def get_locked_file_infos(self, req: Optional[AutolockGetlockedfileinfosReq] = None) -> Optional[Union[AutolockGetlockedfileinfosRes, Error, str]]:
        """获取当前用户锁信息 (对应 /autolock/getlockedfileinfos)
        
        如果 req 为 None，则获取所有记录。
        """
        # If req is None, pass an empty AutolockGetlockedfileinfosReq instance
        # as api_request expects a BaseModel for req_data.
        request_data = req if req is not None else AutolockGetlockedfileinfosReq()
        
        return self.api_request(
            relative_path="/autolock/getlockedfileinfos",
            req_data=request_data,
            success_model=AutolockGetlockedfileinfosRes
        ) 