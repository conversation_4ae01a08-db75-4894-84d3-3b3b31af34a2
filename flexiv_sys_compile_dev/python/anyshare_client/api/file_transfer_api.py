from .base_api import Base<PERSON><PERSON>
from anyshare_client.config import Config
from .models import *
from typing import Optional, List, Dict, Any, Tuple, Union


import requests
import json
import os
import time # For client_mtime, and potentially Date headers if not provided by API
import math
from urllib.parse import urljoin, urlparse, urlunparse # Added urlparse, urlunparse
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# --- Constants ---
_SINGLE_UPLOAD_MAX_BYTES = 5 * 1024 * 1024 * 1024  # 5 GiB
_DEFAULT_MULTIPART_PART_SIZE = 10 * 1024 * 1024    # 10 MB
_MIN_MULTIPART_PART_SIZE_S3_LIKE = 5 * 1024 * 1024 # 5MB (Typical S3 minimum)
_MAX_BATCH_DOWNLOAD_REDIRECTS = 5
_DEFAULT_CHUNK_SIZE = 8192


class FileTransferApi(BaseApi):
    """
    Client for interacting with the AnyShare File Transfer API endpoints.
    This class provides low-level wrappers for the API calls.
    The business logic for uploads/downloads is in AnyshareClient.
    """
    def __init__(self, config: Config):
        """
        Initializes the API client.

        Args:
            config (Config): The configuration object containing base_url, token_id, etc.
        """
        super().__init__(config)
        # self._requests_verify_ssl = False # This setting is now managed in AnyshareClient for direct OSS calls

    def os_option(self) -> Optional[Union[FileOsoptionRes, Error, str]]:
        """
        Gets Object Storage options.
        Corresponds to API: POST /file/osoption (Summary: 对象存储的选项值)
        """
        return self.api_request(
            relative_path="/file/osoption",
            req_data=None, # os_option usually takes no request body or an empty one
            success_model=FileOsoptionRes,
            method="POST" # As per original docstring
        )

    def os_begin_upload(self, req_data: FileOsbeginuploadReq) -> Optional[Union[FileOsbeginuploadRes, Error, str]]:
        """
        Begins the file upload process to Object Storage.
        Corresponds to API: POST /file/osbeginupload (Summary: 开始上传文件协议)
        """
        return self.api_request(
            relative_path="/file/osbeginupload",
            req_data=req_data,
            success_model=FileOsbeginuploadRes
            # Default method is POST if not specified and req_data is present
        )

    def os_end_upload(self, req_data: FileOsenduploadReq) -> Optional[Union[FileOsenduploadRes, Error, str]]:
        """
        Finalizes a file upload to Object Storage.
        Corresponds to API: POST /file/osendupload (Summary: 上传文件完成协议)
        """
        return self.api_request(
            relative_path="/file/osendupload",
            req_data=req_data,
            success_model=FileOsenduploadRes
        )

    def os_download(self, req_data: FileOsdownloadReq) -> Optional[Union[FileOsdownloadRes, Error, str]]:
        """
        Gets information and authorization for downloading a file from Object Storage.
        Corresponds to API: POST /file/osdownload (Summary: 下载文件协议)
        """
        return self.api_request(
            relative_path="/file/osdownload",
            req_data=req_data,
            success_model=FileOsdownloadRes
        )

    def os_init_multiupload(self, req_data: FileOsinitmultiuploadReq) -> Optional[Union[FileOsinitmultiuploadRes, Error, str]]:
        """
        Initializes a multipart upload.
        Corresponds to API: POST /file/osinitmultiupload (Summary: 开始上传大文件协议)
        """
        return self.api_request(
            relative_path="/file/osinitmultiupload",
            req_data=req_data,
            success_model=FileOsinitmultiuploadRes
        )

    def os_upload_part(self, req_data: FileOsuploadpartReq) -> Optional[Union[FileOsuploadpartRes, Error, str]]:
        """
        Gets authorization for uploading a part of a large file.
        Corresponds to API: POST /file/osuploadpart (Summary: 上传大文件的分块协议)
        """
        return self.api_request(
            relative_path="/file/osuploadpart",
            req_data=req_data,
            success_model=FileOsuploadpartRes
        )

    def os_complete_upload(self, req_data: FileOscompleteuploadReq) -> Optional[Union[FileOscompleteuploadRes, Error, str]]:
        """
        Completes a multipart upload.
        Corresponds to API: POST /file/oscompleteupload (Summary: 上传大文件的分块完成协议)
        """
        return self.api_request(
            relative_path="/file/oscompleteupload",
            req_data=req_data,
            success_model=FileOscompleteuploadRes 
        )

    def os_upload_refresh(self, req_data: FileOsuploadrefreshReq) -> Optional[Union[FileOsuploadrefreshRes, Error, str]]:
        """
        Refreshes an upload, can be used to update version size or get new auth request.
        Corresponds to API: POST /file/osuploadrefresh (Summary: 上传文件更新协议)
        """
        return self.api_request(
            relative_path="/file/osuploadrefresh",
            req_data=req_data,
            success_model=FileOsuploadrefreshRes
        )

    def batch_download(self, req_data: FileBatchdownloadReq) -> Optional[Union[FileBatchdownloadRes, Error, str]]:
        """
        Initiates a batch download of files and folders.
        Corresponds to API: POST /file/batchdownload (Summary: 文件及文件夹批量下载)
        """
        return self.api_request(
            relative_path="/file/batchdownload",
            req_data=req_data,
            success_model=FileBatchdownloadRes
        )

    # --- All high-level workflow methods and internal helpers like _parse_auth_info_for_oss, 
    # --- _upload_to_oss, _download_from_oss, _upload_file_parts, _handle_api_response 
    # --- and constants like _SINGLE_UPLOAD_MAX_BYTES have been moved to AnyshareClient.



