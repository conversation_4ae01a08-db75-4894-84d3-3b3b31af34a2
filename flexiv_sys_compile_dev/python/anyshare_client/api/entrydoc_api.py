from .base_api import Base<PERSON>pi
from anyshare_client.config import Config
from .models import *
from typing import Optional, Union

class EntryDocApi(BaseApi):
    def __init__(self, config: Config):
        super().__init__(config)

    def get_accessible_doc_libs(self, req_data: Entrydoc2GetReq) -> Optional[Union[Entrydoc2GetRes, Error, str]]:
        """获取用户可访问的文档库列表 (POST /entrydoc2/get)

        Args:
            req_data (Entrydoc2GetReq): 请求参数模型

        Returns:
            Optional[Union[Entrydoc2GetRes, Error, str]]: API响应的模型，或在请求失败时返回None或错误信息。
        """
        relative_path = "entrydoc2/get"
        return self.api_request(relative_path=relative_path, req_data=req_data, success_model=Entrydoc2GetRes)

    def get_managed_doc_libs(self) -> Optional[Union[Entrydoc2GetmanagedRes, Error, str]]:
        """获取用户管理的文档库列表 (POST /entrydoc2/getmanaged)

        Returns:
            Optional[Union[Entrydoc2GetmanagedRes, Error, str]]: API响应的模型，或在请求失败时返回None或错误信息。
        """
        relative_path = "entrydoc2/getmanaged"
        return self.api_request(relative_path=relative_path, success_model=Entrydoc2GetmanagedRes)

    def get_doc_lib_quota(self, req_data: Entrydoc2GetdocquotaReq) -> Optional[Union[Entrydoc2GetdocquotaRes, Error, str]]:
        """获取文档库配额信息 (POST /entrydoc2/getdocquota)

        Args:
            req_data (Entrydoc2GetdocquotaReq): 请求参数模型.

        Returns:
            Optional[Union[Entrydoc2GetdocquotaRes, Error, str]]: API响应的模型，或在请求失败时返回None或错误信息。
        """
        relative_path = "entrydoc2/getdocquota"
        return self.api_request(relative_path=relative_path, req_data=req_data, success_model=Entrydoc2GetdocquotaRes)

    def get_user_quota(self) -> Optional[Union[Entrydoc2GetuserquotaRes, Error, str]]:
        """获取用户配额 (POST /entrydoc2/getuserquota)

        Returns:
            Optional[Union[Entrydoc2GetuserquotaRes, Error, str]]: API响应的模型，或在请求失败时返回None或错误信息。
        """
        relative_path = "entrydoc2/getuserquota"
        return self.api_request(relative_path=relative_path, success_model=Entrydoc2GetuserquotaRes)

    def get_doc_lib_info(self, req_data: Entrydoc2GetdocinfoReq) -> Optional[Union[Entrydoc2GetdocinfoRes, Error, str]]:
        """获取文档库详细信息 (POST /entrydoc2/getdocinfo)

        Args:
            req_data (Entrydoc2GetdocinfoReq): 请求参数模型.

        Returns:
            Optional[Union[Entrydoc2GetdocinfoRes, Error, str]]: API响应的模型，或在请求失败时返回None或错误信息。
        """
        relative_path = "entrydoc2/getdocinfo"
        return self.api_request(relative_path=relative_path, req_data=req_data, success_model=Entrydoc2GetdocinfoRes) 